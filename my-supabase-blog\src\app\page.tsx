import Link from 'next/link'
import { createClient } from '@/lib/supabase/server'
import { getCurrentUser, isAdmin } from '@/lib/auth'
import { Post } from '@/types/database'
import { ClientThemeToggle } from '@/components/client-theme-toggle'

async function getPosts(): Promise<Post[]> {
  const supabase = await createClient()
  const { data: posts, error } = await supabase
    .from('posts')
    .select('*')
    .order('created_at', { ascending: false })

  if (error) {
    console.error('Error fetching posts:', error)
    return []
  }

  return posts || []
}

export default async function Home() {
  const posts = await getPosts()
  const user = await getCurrentUser()
  const userIsAdmin = user ? await isAdmin(user.id) : false

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <header className="bg-card shadow-sm border-b border-border">
        <div className="max-w-4xl mx-auto px-4 py-6">
          <div className="flex justify-between items-center">
            <h1 className="text-3xl font-bold text-foreground">我的部落格</h1>
            <div className="flex items-center gap-4">
              <ClientThemeToggle />
              {user ? (
                <>
                  <span className="text-sm text-muted-foreground">
                    歡迎, {user.email}
                  </span>
                  {userIsAdmin && (
                    <Link
                      href="/admin/new-post"
                      className="bg-primary text-primary-foreground px-4 py-2 rounded-md hover:bg-primary/90 transition-colors"
                    >
                      新增文章
                    </Link>
                  )}
                  {userIsAdmin && (
                    <Link
                      href="/admin/manage-posts"
                      className="bg-secondary text-secondary-foreground px-4 py-2 rounded-md hover:bg-secondary/80 transition-colors"
                    >
                      管理文章
                    </Link>
                  )}
                  <form action="/auth/signout" method="post">
                    <button
                      type="submit"
                      className="text-muted-foreground hover:text-foreground transition-colors"
                    >
                      登出
                    </button>
                  </form>
                </>
              ) : (
                <Link
                  href="/login"
                  className="bg-primary text-primary-foreground px-4 py-2 rounded-md hover:bg-primary/90 transition-colors"
                >
                  登入
                </Link>
              )}
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-4xl mx-auto px-4 py-8">
        {posts.length === 0 ? (
          <div className="text-center py-12">
            <h2 className="text-xl text-muted-foreground">目前沒有文章</h2>
            {userIsAdmin && (
              <p className="mt-2 text-muted-foreground">
                <Link href="/admin/new-post" className="text-primary hover:underline">
                  建立第一篇文章
                </Link>
              </p>
            )}
          </div>
        ) : (
          <div className="space-y-6">
            {posts.map((post, index) => (
              <article
                key={post.id}
                className="bg-card rounded-lg shadow-sm border border-border p-6 hover-lift animate-fade-in transition-all duration-200 hover:border-primary/20"
                style={{ animationDelay: `${index * 0.1}s` }}
              >
                <h2 className="text-2xl font-semibold text-card-foreground mb-3">
                  <Link
                    href={`/posts/${post.id}`}
                    className="hover:text-primary transition-colors"
                  >
                    {post.title}
                  </Link>
                </h2>
                <p className="text-muted-foreground mb-4 line-clamp-3">
                  {post.content.substring(0, 200)}
                  {post.content.length > 200 && '...'}
                </p>
                <div className="flex justify-between items-center">
                  <time className="text-sm text-muted-foreground flex items-center">
                    <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                    </svg>
                    {new Date(post.created_at).toLocaleDateString('zh-TW')}
                  </time>
                  <Link
                    href={`/posts/${post.id}`}
                    className="text-primary hover:text-primary/80 font-medium transition-colors group inline-flex items-center"
                  >
                    閱讀更多
                    <svg className="w-4 h-4 ml-1 group-hover:translate-x-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                    </svg>
                  </Link>
                </div>
              </article>
            ))}
          </div>
        )}
      </main>
    </div>
  )
}
