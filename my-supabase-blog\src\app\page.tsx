import Link from 'next/link'
import { createClient } from '@/lib/supabase/server'
import { getCurrentUser, isAdmin } from '@/lib/auth'
import { Post } from '@/types/database'
import { ClientThemeToggle } from '@/components/client-theme-toggle'

async function getPosts(): Promise<Post[]> {
  const supabase = await createClient()
  const { data: posts, error } = await supabase
    .from('posts')
    .select('*')
    .order('created_at', { ascending: false })

  if (error) {
    console.error('Error fetching posts:', error)
    return []
  }

  return posts || []
}

export default async function Home() {
  const posts = await getPosts()
  const user = await getCurrentUser()
  const userIsAdmin = user ? await isAdmin(user.id) : false

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <header className="bg-card/95 shadow-sm border-b border-border sticky top-0 z-50 backdrop-blur-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4 lg:py-6">
          <div className="flex justify-between items-center">
            <h1 className="text-2xl sm:text-3xl font-bold text-foreground">My Blog</h1>
            <div className="flex items-center gap-2 sm:gap-4">
              <ClientThemeToggle />
              {user ? (
                <>
                  <span className="hidden sm:block text-sm text-muted-foreground">
                    Welcome, {user.email}
                  </span>
                  {userIsAdmin && (
                    <Link
                      href="/admin/new-post"
                      className="bg-primary text-primary-foreground px-3 sm:px-4 py-2 rounded-md hover:bg-primary/90 transition-all duration-200 text-sm font-medium shadow-sm hover:shadow-md"
                    >
                      <span className="hidden sm:inline">New Post</span>
                      <span className="sm:hidden">+</span>
                    </Link>
                  )}
                  {userIsAdmin && (
                    <Link
                      href="/admin/manage-posts"
                      className="bg-secondary text-secondary-foreground px-3 sm:px-4 py-2 rounded-md hover:bg-secondary/80 transition-all duration-200 text-sm font-medium shadow-sm hover:shadow-md"
                    >
                      <span className="hidden sm:inline">Manage</span>
                      <span className="sm:hidden">⚙️</span>
                    </Link>
                  )}
                  <form action="/auth/signout" method="post">
                    <button
                      type="submit"
                      className="inline-flex items-center justify-center px-3 py-2 rounded-md text-sm font-medium transition-colors bg-destructive text-destructive-foreground hover:bg-destructive/90 shadow-sm hover:shadow-md"
                    >
                      <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
                      </svg>
                      Sign Out
                    </button>
                  </form>
                </>
              ) : (
                <Link
                  href="/login"
                  className="bg-primary text-primary-foreground px-3 sm:px-4 py-2 rounded-md hover:bg-primary/90 transition-all duration-200 text-sm font-medium shadow-sm hover:shadow-md"
                >
                  Sign In
                </Link>
              )}
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8 lg:py-12">
        {posts.length === 0 ? (
          <div className="text-center py-16 lg:py-24">
            <div className="max-w-md mx-auto">
              <div className="w-16 h-16 mx-auto mb-6 bg-muted rounded-full flex items-center justify-center">
                <svg className="w-8 h-8 text-muted-foreground" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
              </div>
              <h2 className="text-2xl font-semibold text-foreground mb-2">No posts yet</h2>
              <p className="text-muted-foreground mb-6">Start sharing your thoughts with the world!</p>
              {userIsAdmin && (
                <Link
                  href="/admin/new-post"
                  className="inline-flex items-center px-6 py-3 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-all duration-200 font-medium shadow-sm hover:shadow-md"
                >
                  <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
                  </svg>
                  Create your first post
                </Link>
              )}
            </div>
          </div>
        ) : (
          <div className="grid gap-6 lg:gap-8">
            {posts.map((post, index) => (
              <article
                key={post.id}
                className="bg-card rounded-xl shadow-sm border border-border p-6 lg:p-8 hover-lift animate-fade-in transition-all duration-300 hover:border-primary/20 hover:shadow-lg group"
                style={{ animationDelay: `${index * 0.1}s` }}
              >
                <div className="flex flex-col lg:flex-row lg:items-start lg:justify-between gap-4">
                  <div className="flex-1 min-w-0">
                    <h2 className="text-xl lg:text-2xl font-semibold text-card-foreground mb-3 group-hover:text-primary transition-colors">
                      <Link
                        href={`/posts/${post.id}`}
                        className="block"
                      >
                        {post.title}
                      </Link>
                    </h2>
                    <p className="text-muted-foreground mb-4 line-clamp-3 leading-relaxed">
                      {post.content.substring(0, 200)}
                      {post.content.length > 200 && '...'}
                    </p>
                    <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3">
                      <time className="text-sm text-muted-foreground flex items-center">
                        <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                        </svg>
                        {new Date(post.created_at).toLocaleDateString('en-US', {
                          year: 'numeric',
                          month: 'long',
                          day: 'numeric'
                        })}
                      </time>
                      <Link
                        href={`/posts/${post.id}`}
                        className="text-primary hover:text-primary/80 font-medium transition-all duration-200 group/link inline-flex items-center text-sm"
                      >
                        Read more
                        <svg className="w-4 h-4 ml-1 group-hover/link:translate-x-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                        </svg>
                      </Link>
                    </div>
                  </div>
                </div>
              </article>
            ))}
          </div>
        )}
      </main>
    </div>
  )
}
