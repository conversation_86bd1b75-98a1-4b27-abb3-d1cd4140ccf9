{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 104, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test2/my-supabase-blog/src/lib/supabase/server.ts"], "sourcesContent": ["import { createServerClient } from '@supabase/ssr'\nimport { cookies } from 'next/headers'\n\nexport async function createClient() {\n  const cookieStore = await cookies()\n\n  return createServerClient(\n    process.env.NEXT_PUBLIC_SUPABASE_URL!,\n    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,\n    {\n      cookies: {\n        getAll() {\n          return cookieStore.getAll()\n        },\n        setAll(cookiesToSet) {\n          try {\n            cookiesToSet.forEach(({ name, value, options }) =>\n              cookieStore.set(name, value, options)\n            )\n          } catch {\n            // The `setAll` method was called from a Server Component.\n            // This can be ignored if you have middleware refreshing\n            // user sessions.\n          }\n        },\n      },\n    }\n  )\n}\n"], "names": [], "mappings": ";;;AAAA;AAAA;AACA;;;AAEO,eAAe;IACpB,MAAM,cAAc,MAAM,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAEhC,OAAO,CAAA,GAAA,yKAAA,CAAA,qBAAkB,AAAD,sUAGtB;QACE,SAAS;YACP;gBACE,OAAO,YAAY,MAAM;YAC3B;YACA,QAAO,YAAY;gBACjB,IAAI;oBACF,aAAa,OAAO,CAAC,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,OAAO,EAAE,GAC5C,YAAY,GAAG,CAAC,MAAM,OAAO;gBAEjC,EAAE,OAAM;gBACN,0DAA0D;gBAC1D,wDAAwD;gBACxD,iBAAiB;gBACnB;YACF;QACF;IACF;AAEJ", "debugId": null}}, {"offset": {"line": 137, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test2/my-supabase-blog/src/lib/auth.ts"], "sourcesContent": ["import { createClient } from '@/lib/supabase/server'\n\nexport async function getCurrentUser() {\n  const supabase = await createClient()\n  const { data: { user } } = await supabase.auth.getUser()\n  return user\n}\n\nexport async function isAdmin(userId?: string) {\n  if (!userId) return false\n\n  const supabase = await createClient()\n\n  // 方法1: 檢查 profiles 表中的 is_admin 欄位\n  const { data: profile } = await supabase\n    .from('profiles')\n    .select('is_admin')\n    .eq('id', userId)\n    .single()\n\n  if (profile?.is_admin) return true\n\n  // 方法2: 檢查是否為預設管理員 email\n  const { data: { user } } = await supabase.auth.getUser()\n  const adminEmail = process.env.ADMIN_EMAIL\n\n  return user?.email === adminEmail\n}\n\nexport async function requireAdmin() {\n  const user = await getCurrentUser()\n  if (!user) {\n    throw new Error('未登入')\n  }\n\n  const isUserAdmin = await isAdmin(user.id)\n  if (!isUserAdmin) {\n    throw new Error('需要管理員權限')\n  }\n\n  return user\n}\n"], "names": [], "mappings": ";;;;;AAAA;;AAEO,eAAe;IACpB,MAAM,WAAW,MAAM,CAAA,GAAA,gIAAA,CAAA,eAAY,AAAD;IAClC,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;IACtD,OAAO;AACT;AAEO,eAAe,QAAQ,MAAe;IAC3C,IAAI,CAAC,QAAQ,OAAO;IAEpB,MAAM,WAAW,MAAM,CAAA,GAAA,gIAAA,CAAA,eAAY,AAAD;IAElC,mCAAmC;IACnC,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,MAAM,SAC7B,IAAI,CAAC,YACL,MAAM,CAAC,YACP,EAAE,CAAC,MAAM,QACT,MAAM;IAET,IAAI,SAAS,UAAU,OAAO;IAE9B,wBAAwB;IACxB,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;IACtD,MAAM,aAAa,QAAQ,GAAG,CAAC,WAAW;IAE1C,OAAO,MAAM,UAAU;AACzB;AAEO,eAAe;IACpB,MAAM,OAAO,MAAM;IACnB,IAAI,CAAC,MAAM;QACT,MAAM,IAAI,MAAM;IAClB;IAEA,MAAM,cAAc,MAAM,QAAQ,KAAK,EAAE;IACzC,IAAI,CAAC,aAAa;QAChB,MAAM,IAAI,MAAM;IAClB;IAEA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 177, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test2/my-supabase-blog/src/components/theme-toggle.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const ThemeToggle = registerClientReference(\n    function() { throw new Error(\"Attempted to call ThemeToggle() from the server but ThemeToggle is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/theme-toggle.tsx <module evaluation>\",\n    \"ThemeToggle\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,cAAc,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC7C;IAAa,MAAM,IAAI,MAAM;AAAsO,GACnQ,iEACA", "debugId": null}}, {"offset": {"line": 191, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test2/my-supabase-blog/src/components/theme-toggle.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const ThemeToggle = registerClientReference(\n    function() { throw new Error(\"Attempted to call ThemeToggle() from the server but ThemeToggle is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/theme-toggle.tsx\",\n    \"ThemeToggle\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,cAAc,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC7C;IAAa,MAAM,IAAI,MAAM;AAAsO,GACnQ,6CACA", "debugId": null}}, {"offset": {"line": 205, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 215, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test2/my-supabase-blog/src/app/page.tsx"], "sourcesContent": ["import Link from 'next/link'\nimport { createClient } from '@/lib/supabase/server'\nimport { getCurrentUser, isAdmin } from '@/lib/auth'\nimport { Post } from '@/types/database'\nimport { ThemeToggle } from '@/components/theme-toggle'\n\nasync function getPosts(): Promise<Post[]> {\n  const supabase = await createClient()\n  const { data: posts, error } = await supabase\n    .from('posts')\n    .select('*')\n    .order('created_at', { ascending: false })\n\n  if (error) {\n    console.error('Error fetching posts:', error)\n    return []\n  }\n\n  return posts || []\n}\n\nexport default async function Home() {\n  const posts = await getPosts()\n  const user = await getCurrentUser()\n  const userIsAdmin = user ? await isAdmin(user.id) : false\n\n  return (\n    <div className=\"min-h-screen bg-background\">\n      {/* Header */}\n      <header className=\"bg-card shadow-sm border-b border-border\">\n        <div className=\"max-w-4xl mx-auto px-4 py-6\">\n          <div className=\"flex justify-between items-center\">\n            <h1 className=\"text-3xl font-bold text-foreground\">我的部落格</h1>\n            <div className=\"flex items-center gap-4\">\n              <ThemeToggle />\n              {user ? (\n                <>\n                  <span className=\"text-sm text-muted-foreground\">\n                    歡迎, {user.email}\n                  </span>\n                  {userIsAdmin && (\n                    <Link\n                      href=\"/admin/new-post\"\n                      className=\"bg-primary text-primary-foreground px-4 py-2 rounded-md hover:bg-primary/90 transition-colors\"\n                    >\n                      新增文章\n                    </Link>\n                  )}\n                  {userIsAdmin && (\n                    <Link\n                      href=\"/admin/manage-posts\"\n                      className=\"bg-secondary text-secondary-foreground px-4 py-2 rounded-md hover:bg-secondary/80 transition-colors\"\n                    >\n                      管理文章\n                    </Link>\n                  )}\n                  <form action=\"/auth/signout\" method=\"post\">\n                    <button\n                      type=\"submit\"\n                      className=\"text-muted-foreground hover:text-foreground transition-colors\"\n                    >\n                      登出\n                    </button>\n                  </form>\n                </>\n              ) : (\n                <Link\n                  href=\"/login\"\n                  className=\"bg-primary text-primary-foreground px-4 py-2 rounded-md hover:bg-primary/90 transition-colors\"\n                >\n                  登入\n                </Link>\n              )}\n            </div>\n          </div>\n        </div>\n      </header>\n\n      {/* Main Content */}\n      <main className=\"max-w-4xl mx-auto px-4 py-8\">\n        {posts.length === 0 ? (\n          <div className=\"text-center py-12\">\n            <h2 className=\"text-xl text-muted-foreground\">目前沒有文章</h2>\n            {userIsAdmin && (\n              <p className=\"mt-2 text-muted-foreground\">\n                <Link href=\"/admin/new-post\" className=\"text-primary hover:underline\">\n                  建立第一篇文章\n                </Link>\n              </p>\n            )}\n          </div>\n        ) : (\n          <div className=\"space-y-6\">\n            {posts.map((post, index) => (\n              <article\n                key={post.id}\n                className=\"bg-card rounded-lg shadow-sm border border-border p-6 hover-lift animate-fade-in transition-all duration-200 hover:border-primary/20\"\n                style={{ animationDelay: `${index * 0.1}s` }}\n              >\n                <h2 className=\"text-2xl font-semibold text-card-foreground mb-3\">\n                  <Link\n                    href={`/posts/${post.id}`}\n                    className=\"hover:text-primary transition-colors\"\n                  >\n                    {post.title}\n                  </Link>\n                </h2>\n                <p className=\"text-muted-foreground mb-4 line-clamp-3\">\n                  {post.content.substring(0, 200)}\n                  {post.content.length > 200 && '...'}\n                </p>\n                <div className=\"flex justify-between items-center\">\n                  <time className=\"text-sm text-muted-foreground flex items-center\">\n                    <svg className=\"w-4 h-4 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z\" />\n                    </svg>\n                    {new Date(post.created_at).toLocaleDateString('zh-TW')}\n                  </time>\n                  <Link\n                    href={`/posts/${post.id}`}\n                    className=\"text-primary hover:text-primary/80 font-medium transition-colors group inline-flex items-center\"\n                  >\n                    閱讀更多\n                    <svg className=\"w-4 h-4 ml-1 group-hover:translate-x-1 transition-transform\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 5l7 7-7 7\" />\n                    </svg>\n                  </Link>\n                </div>\n              </article>\n            ))}\n          </div>\n        )}\n      </main>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AAEA;;;;;;AAEA,eAAe;IACb,MAAM,WAAW,MAAM,CAAA,GAAA,gIAAA,CAAA,eAAY,AAAD;IAClC,MAAM,EAAE,MAAM,KAAK,EAAE,KAAK,EAAE,GAAG,MAAM,SAClC,IAAI,CAAC,SACL,MAAM,CAAC,KACP,KAAK,CAAC,cAAc;QAAE,WAAW;IAAM;IAE1C,IAAI,OAAO;QACT,QAAQ,KAAK,CAAC,yBAAyB;QACvC,OAAO,EAAE;IACX;IAEA,OAAO,SAAS,EAAE;AACpB;AAEe,eAAe;IAC5B,MAAM,QAAQ,MAAM;IACpB,MAAM,OAAO,MAAM,CAAA,GAAA,kHAAA,CAAA,iBAAc,AAAD;IAChC,MAAM,cAAc,OAAO,MAAM,CAAA,GAAA,kHAAA,CAAA,UAAO,AAAD,EAAE,KAAK,EAAE,IAAI;IAEpD,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAqC;;;;;;0CACnD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,qIAAA,CAAA,cAAW;;;;;oCACX,qBACC;;0DACE,8OAAC;gDAAK,WAAU;;oDAAgC;oDACzC,KAAK,KAAK;;;;;;;4CAEhB,6BACC,8OAAC,4JAAA,CAAA,UAAI;gDACH,MAAK;gDACL,WAAU;0DACX;;;;;;4CAIF,6BACC,8OAAC,4JAAA,CAAA,UAAI;gDACH,MAAK;gDACL,WAAU;0DACX;;;;;;0DAIH,8OAAC;gDAAK,QAAO;gDAAgB,QAAO;0DAClC,cAAA,8OAAC;oDACC,MAAK;oDACL,WAAU;8DACX;;;;;;;;;;;;qEAML,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAUX,8OAAC;gBAAK,WAAU;0BACb,MAAM,MAAM,KAAK,kBAChB,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAgC;;;;;;wBAC7C,6BACC,8OAAC;4BAAE,WAAU;sCACX,cAAA,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAkB,WAAU;0CAA+B;;;;;;;;;;;;;;;;yCAO5E,8OAAC;oBAAI,WAAU;8BACZ,MAAM,GAAG,CAAC,CAAC,MAAM,sBAChB,8OAAC;4BAEC,WAAU;4BACV,OAAO;gCAAE,gBAAgB,GAAG,QAAQ,IAAI,CAAC,CAAC;4BAAC;;8CAE3C,8OAAC;oCAAG,WAAU;8CACZ,cAAA,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAM,CAAC,OAAO,EAAE,KAAK,EAAE,EAAE;wCACzB,WAAU;kDAET,KAAK,KAAK;;;;;;;;;;;8CAGf,8OAAC;oCAAE,WAAU;;wCACV,KAAK,OAAO,CAAC,SAAS,CAAC,GAAG;wCAC1B,KAAK,OAAO,CAAC,MAAM,GAAG,OAAO;;;;;;;8CAEhC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAK,WAAU;;8DACd,8OAAC;oDAAI,WAAU;oDAAe,MAAK;oDAAO,QAAO;oDAAe,SAAQ;8DACtE,cAAA,8OAAC;wDAAK,eAAc;wDAAQ,gBAAe;wDAAQ,aAAa;wDAAG,GAAE;;;;;;;;;;;gDAEtE,IAAI,KAAK,KAAK,UAAU,EAAE,kBAAkB,CAAC;;;;;;;sDAEhD,8OAAC,4JAAA,CAAA,UAAI;4CACH,MAAM,CAAC,OAAO,EAAE,KAAK,EAAE,EAAE;4CACzB,WAAU;;gDACX;8DAEC,8OAAC;oDAAI,WAAU;oDAA8D,MAAK;oDAAO,QAAO;oDAAe,SAAQ;8DACrH,cAAA,8OAAC;wDAAK,eAAc;wDAAQ,gBAAe;wDAAQ,aAAa;wDAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;;;2BA7BtE,KAAK,EAAE;;;;;;;;;;;;;;;;;;;;;AAwC5B", "debugId": null}}]}