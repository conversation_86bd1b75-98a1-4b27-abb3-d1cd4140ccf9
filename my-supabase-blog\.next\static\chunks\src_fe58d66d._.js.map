{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test2/my-supabase-blog/src/components/markdown-renderer.tsx"], "sourcesContent": ["'use client'\n\nimport dynamic from 'next/dynamic'\nimport { useState, useEffect } from 'react'\nimport '@uiw/react-markdown-preview/markdown.css'\n\n// Dynamically import MDEditor.Markdown to avoid SSR issues\nconst MDPreview = dynamic(\n  () => import('@uiw/react-md-editor').then(mod => ({ default: mod.default.Markdown })),\n  { ssr: false }\n)\n\ninterface MarkdownRendererProps {\n  content: string\n  className?: string\n}\n\nexport function MarkdownRenderer({ content, className = '' }: MarkdownRendererProps) {\n  const [mounted, setMounted] = useState(false)\n\n  useEffect(() => {\n    setMounted(true)\n  }, [])\n\n  // Show loading state during SSR or fallback to simple rendering\n  if (!mounted) {\n    const renderContent = (text: string) => {\n      return text\n        .split('\\n')\n        .map((line, index) => (\n          <div key={index} className=\"mb-2\">\n            {line || <br />}\n          </div>\n        ))\n    }\n\n    return (\n      <div className={`markdown-renderer ${className}`}>\n        <div className=\"prose prose-lg max-w-none dark:prose-invert text-foreground\">\n          {renderContent(content)}\n        </div>\n      </div>\n    )\n  }\n\n  return (\n    <div className={`markdown-renderer ${className}`} data-color-mode=\"auto\">\n      <MDPreview\n        source={content}\n        style={{\n          whiteSpace: 'pre-wrap',\n          backgroundColor: 'transparent',\n          color: 'inherit'\n        }}\n      />\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;;;;AAHA;;;;AAMA,2DAA2D;AAC3D,MAAM,YAAY,CAAA,GAAA,kKAAA,CAAA,UAAO,AAAD,EACtB,IAAM,yKAA+B,IAAI,CAAC,CAAA,MAAO,CAAC;YAAE,SAAS,IAAI,OAAO,CAAC,QAAQ;QAAC,CAAC;;;;;;IACjF,KAAK;;KAFH;AAUC,SAAS,iBAAiB,EAAE,OAAO,EAAE,YAAY,EAAE,EAAyB;;IACjF,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR,WAAW;QACb;qCAAG,EAAE;IAEL,gEAAgE;IAChE,IAAI,CAAC,SAAS;QACZ,MAAM,gBAAgB,CAAC;YACrB,OAAO,KACJ,KAAK,CAAC,MACN,GAAG,CAAC,CAAC,MAAM,sBACV,6LAAC;oBAAgB,WAAU;8BACxB,sBAAQ,6LAAC;;;;;mBADF;;;;;QAIhB;QAEA,qBACE,6LAAC;YAAI,WAAW,CAAC,kBAAkB,EAAE,WAAW;sBAC9C,cAAA,6LAAC;gBAAI,WAAU;0BACZ,cAAc;;;;;;;;;;;IAIvB;IAEA,qBACE,6LAAC;QAAI,WAAW,CAAC,kBAAkB,EAAE,WAAW;QAAE,mBAAgB;kBAChE,cAAA,6LAAC;YACC,QAAQ;YACR,OAAO;gBACL,YAAY;gBACZ,iBAAiB;gBACjB,OAAO;YACT;;;;;;;;;;;AAIR;GAxCgB;MAAA", "debugId": null}}, {"offset": {"line": 107, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test2/my-supabase-blog/src/components/theme-toggle.tsx"], "sourcesContent": ["'use client'\n\nimport * as React from 'react'\nimport { Moon, Sun } from 'lucide-react'\nimport { useTheme } from 'next-themes'\n\nexport function ThemeToggle() {\n  const { theme, setTheme } = useTheme()\n  const [mounted, setMounted] = React.useState(false)\n\n  React.useEffect(() => {\n    setMounted(true)\n  }, [])\n\n  if (!mounted) {\n    return (\n      <button className=\"inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none ring-offset-background border border-input bg-background hover:bg-accent hover:text-accent-foreground h-10 w-10\">\n        <Sun className=\"h-[1.2rem] w-[1.2rem]\" />\n        <span className=\"sr-only\">切換主題</span>\n      </button>\n    )\n  }\n\n  return (\n    <button\n      onClick={() => setTheme(theme === 'light' ? 'dark' : 'light')}\n      className=\"inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none ring-offset-background border border-input bg-background hover:bg-accent hover:text-accent-foreground h-10 w-10\"\n    >\n      {theme === 'light' ? (\n        <Moon className=\"h-[1.2rem] w-[1.2rem]\" />\n      ) : (\n        <Sun className=\"h-[1.2rem] w-[1.2rem]\" />\n      )}\n      <span className=\"sr-only\">切換主題</span>\n    </button>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;;;AAJA;;;;AAMO,SAAS;;IACd,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,mJAAA,CAAA,WAAQ,AAAD;IACnC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAc,AAAD,EAAE;IAE7C,CAAA,GAAA,6JAAA,CAAA,YAAe,AAAD;iCAAE;YACd,WAAW;QACb;gCAAG,EAAE;IAEL,IAAI,CAAC,SAAS;QACZ,qBACE,6LAAC;YAAO,WAAU;;8BAChB,6LAAC,mMAAA,CAAA,MAAG;oBAAC,WAAU;;;;;;8BACf,6LAAC;oBAAK,WAAU;8BAAU;;;;;;;;;;;;IAGhC;IAEA,qBACE,6LAAC;QACC,SAAS,IAAM,SAAS,UAAU,UAAU,SAAS;QACrD,WAAU;;YAET,UAAU,wBACT,6LAAC,qMAAA,CAAA,OAAI;gBAAC,WAAU;;;;;qCAEhB,6LAAC,mMAAA,CAAA,MAAG;gBAAC,WAAU;;;;;;0BAEjB,6LAAC;gBAAK,WAAU;0BAAU;;;;;;;;;;;;AAGhC;GA9BgB;;QACc,mJAAA,CAAA,WAAQ;;;KADtB", "debugId": null}}, {"offset": {"line": 205, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test2/my-supabase-blog/src/components/client-theme-toggle.tsx"], "sourcesContent": ["'use client'\r\n\r\nimport { ThemeToggle } from './theme-toggle'\r\n\r\nexport function ClientThemeToggle() {\r\n  return <ThemeToggle />\r\n}"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAIO,SAAS;IACd,qBAAO,6LAAC,wIAAA,CAAA,cAAW;;;;;AACrB;KAFgB", "debugId": null}}, {"offset": {"line": 232, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test2/my-supabase-blog/src/lib/supabase/client.ts"], "sourcesContent": ["import { createBrowserClient } from '@supabase/ssr'\n\nexport function createClient() {\n  return createBrowserClient(\n    process.env.NEXT_PUBLIC_SUPABASE_URL!,\n    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!\n  )\n}\n"], "names": [], "mappings": ";;;AAII;AAJJ;AAAA;;AAEO,SAAS;IACd,OAAO,CAAA,GAAA,6KAAA,CAAA,sBAAmB,AAAD;AAI3B", "debugId": null}}, {"offset": {"line": 251, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test2/my-supabase-blog/src/components/admin-actions.tsx"], "sourcesContent": ["'use client'\r\n\r\nimport { useRouter } from 'next/navigation'\r\nimport { createClient } from '@/lib/supabase/client'\r\nimport Link from 'next/link'\r\n\r\nexport function AdminActions({ postId }: { postId: string }) {\r\n  const router = useRouter()\r\n  const supabase = createClient()\r\n\r\n  const handleDelete = async () => {\r\n    if (!confirm('確定要刪除這篇文章嗎？此操作無法復原。')) return\r\n\r\n    const { error } = await supabase\r\n      .from('posts')\r\n      .delete()\r\n      .eq('id', postId)\r\n\r\n    if (error) {\r\n      alert('刪除文章時發生錯誤: ' + error.message)\r\n    } else {\r\n      alert('文章刪除成功！')\r\n      router.push('/')\r\n    }\r\n  }\r\n\r\n  return (\r\n    <div className=\"flex gap-4\">\r\n      <Link\r\n        href={`/admin/edit-post/${postId}`}\r\n        className=\"bg-primary text-primary-foreground px-4 py-2 rounded-md hover:bg-primary/90 transition-colors\"\r\n      >\r\n        編輯文章\r\n      </Link>\r\n      <button\r\n        className=\"bg-destructive text-destructive-foreground px-4 py-2 rounded-md hover:bg-destructive/90 transition-colors\"\r\n        onClick={handleDelete}\r\n      >\r\n        刪除文章\r\n      </button>\r\n    </div>\r\n  )\r\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAMO,SAAS,aAAa,EAAE,MAAM,EAAsB;;IACzD,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,mIAAA,CAAA,eAAY,AAAD;IAE5B,MAAM,eAAe;QACnB,IAAI,CAAC,QAAQ,wBAAwB;QAErC,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SACrB,IAAI,CAAC,SACL,MAAM,GACN,EAAE,CAAC,MAAM;QAEZ,IAAI,OAAO;YACT,MAAM,gBAAgB,MAAM,OAAO;QACrC,OAAO;YACL,MAAM;YACN,OAAO,IAAI,CAAC;QACd;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,+JAAA,CAAA,UAAI;gBACH,MAAM,CAAC,iBAAiB,EAAE,QAAQ;gBAClC,WAAU;0BACX;;;;;;0BAGD,6LAAC;gBACC,WAAU;gBACV,SAAS;0BACV;;;;;;;;;;;;AAKP;GApCgB;;QACC,qIAAA,CAAA,YAAS;;;KADV", "debugId": null}}]}