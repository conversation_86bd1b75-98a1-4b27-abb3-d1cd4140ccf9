'use client'

import dynamic from 'next/dynamic'
import { useState, useEffect } from 'react'
import '@uiw/react-md-editor/markdown-editor.css'
import '@uiw/react-markdown-preview/markdown.css'

// Dynamically import MDEditor to avoid SSR issues
const MDEditor = dynamic(
  () => import('@uiw/react-md-editor'),
  { ssr: false }
)

interface CherryEditorProps {
  value?: string
  onChange?: (value: string) => void
  height?: string
  placeholder?: string
}

export function CherryEditor({
  value = '',
  onChange,
  height = '500px',
  placeholder = '開始編寫您的文章...'
}: CherryEditorProps) {
  const [mounted, setMounted] = useState(false)

  useEffect(() => {
    setMounted(true)
  }, [])

  // Show loading state during SSR
  if (!mounted) {
    return (
      <div
        className="w-full p-4 border border-input bg-background text-foreground rounded-md animate-pulse"
        style={{ height }}
      >
        <div className="text-muted-foreground">載入編輯器中...</div>
      </div>
    )
  }

  return (
    <div className="w-full" style={{ height }} data-color-mode="auto">
      <MDEditor
        value={value}
        onChange={(val) => onChange?.(val || '')}
        height={parseInt(height.replace('px', ''))}
        preview="live"
        hideToolbar={false}
        visibleDragbar={false}
        textareaProps={{
          placeholder,
          style: {
            fontSize: 14,
            lineHeight: 1.6,
            fontFamily: 'ui-monospace, SFMono-Regular, "SF Mono", Monaco, Consolas, "Liberation Mono", "Courier New", monospace'
          }
        }}
      />
    </div>
  )
}
