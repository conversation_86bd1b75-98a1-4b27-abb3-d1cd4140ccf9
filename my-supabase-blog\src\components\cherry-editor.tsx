'use client'

import { useEffect, useRef, useState } from 'react'
import { useTheme } from 'next-themes'

interface CherryEditorProps {
  value?: string
  onChange?: (value: string) => void
  height?: string
  placeholder?: string
}

export function CherryEditor({ 
  value = '', 
  onChange, 
  height = '500px',
  placeholder = '開始編寫您的文章...'
}: CherryEditorProps) {
  const editorRef = useRef<HTMLDivElement>(null)
  const cherryRef = useRef<any>(null)
  const [isLoaded, setIsLoaded] = useState(false)
  const { theme } = useTheme()

  useEffect(() => {
    const loadCherry = async () => {
      try {
        // 動態導入 Cherry Markdown
        const Cherry = (await import('cherry-markdown')).default
        
        if (editorRef.current && !cherryRef.current) {
          // 為編輯器容器設置唯一 ID
          const editorId = `cherry-editor-${Date.now()}`
          editorRef.current.id = editorId

          cherryRef.current = new Cherry({
            id: editorId,
            value: value,
            editor: {
              height: height,
              defaultModel: 'edit&preview'
            },
            callback: {
              afterChange: (text: string) => {
                if (onChange) {
                  onChange(text)
                }
              }
            }
          } as any)
          
          setIsLoaded(true)
        }
      } catch (error) {
        console.error('Failed to load Cherry Markdown:', error)
      }
    }

    loadCherry()

    return () => {
      if (cherryRef.current) {
        cherryRef.current.destroy()
        cherryRef.current = null
      }
    }
  }, [])

  // 更新主題
  useEffect(() => {
    if (cherryRef.current && isLoaded) {
      try {
        // 更新編輯器主題
        cherryRef.current.setTheme(theme === 'dark' ? 'dark' : 'light')
      } catch (error) {
        console.error('Failed to update theme:', error)
      }
    }
  }, [theme, isLoaded])

  // 更新值
  useEffect(() => {
    if (cherryRef.current && isLoaded && value !== cherryRef.current.getValue()) {
      cherryRef.current.setValue(value)
    }
  }, [value, isLoaded])

  return (
    <div className="cherry-editor-wrapper">
      <div 
        ref={editorRef}
        className="cherry-editor"
        style={{ minHeight: height }}
      />
      {!isLoaded && (
        <div className="flex items-center justify-center h-64 bg-muted rounded-md">
          <div className="text-muted-foreground">載入編輯器中...</div>
        </div>
      )}
    </div>
  )
}
