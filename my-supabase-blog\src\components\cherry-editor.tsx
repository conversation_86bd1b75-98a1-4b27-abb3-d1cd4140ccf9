'use client'

import { useState } from 'react'

interface CherryEditorProps {
  value?: string
  onChange?: (value: string) => void
  height?: string
  placeholder?: string
}

export function CherryEditor({
  value = '',
  onChange,
  height = '500px',
  placeholder = '開始編寫您的文章...'
}: CherryEditorProps) {
  const [content, setContent] = useState(value)

  const handleChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const newValue = e.target.value
    setContent(newValue)
    if (onChange) {
      onChange(newValue)
    }
  }

  return (
    <div className="cherry-editor-wrapper">
      <textarea
        value={content}
        onChange={handleChange}
        placeholder={placeholder}
        className="w-full p-4 border border-input bg-background text-foreground rounded-md focus:outline-none focus:ring-2 focus:ring-ring focus:border-transparent resize-none"
        style={{ height }}
      />
    </div>
  )
}
