{"version": 3, "pages404": true, "caseSensitive": false, "basePath": "", "redirects": [{"source": "/:path+/", "destination": "/:path+", "internal": true, "statusCode": 308, "regex": "^(?:/((?:[^/]+?)(?:/(?:[^/]+?))*))/$"}], "headers": [], "dynamicRoutes": [{"page": "/admin/edit-post/[id]", "regex": "^/admin/edit\\-post/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/admin/edit\\-post/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/posts/[id]", "regex": "^/posts/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/posts/(?<nxtPid>[^/]+?)(?:/)?$"}], "staticRoutes": [{"page": "/", "regex": "^/(?:/)?$", "routeKeys": {}, "namedRegex": "^/(?:/)?$"}, {"page": "/_not-found", "regex": "^/_not\\-found(?:/)?$", "routeKeys": {}, "namedRegex": "^/_not\\-found(?:/)?$"}, {"page": "/admin/manage-posts", "regex": "^/admin/manage\\-posts(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/manage\\-posts(?:/)?$"}, {"page": "/admin/new-post", "regex": "^/admin/new\\-post(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/new\\-post(?:/)?$"}, {"page": "/auth/signout", "regex": "^/auth/signout(?:/)?$", "routeKeys": {}, "namedRegex": "^/auth/signout(?:/)?$"}, {"page": "/favicon.ico", "regex": "^/favicon\\.ico(?:/)?$", "routeKeys": {}, "namedRegex": "^/favicon\\.ico(?:/)?$"}, {"page": "/login", "regex": "^/login(?:/)?$", "routeKeys": {}, "namedRegex": "^/login(?:/)?$"}], "dataRoutes": [], "rsc": {"header": "RSC", "varyHeader": "RSC, Next-Router-State-Tree, Next-Router-Prefetch, Next-Router-Segment-Prefetch", "prefetchHeader": "Next-Router-Prefetch", "didPostponeHeader": "x-nextjs-postponed", "contentTypeHeader": "text/x-component", "suffix": ".rsc", "prefetchSuffix": ".prefetch.rsc", "prefetchSegmentHeader": "Next-Router-Segment-Prefetch", "prefetchSegmentSuffix": ".segment.rsc", "prefetchSegmentDirSuffix": ".segments"}, "rewriteHeaders": {"pathHeader": "x-nextjs-rewritten-path", "queryHeader": "x-nextjs-rewritten-query"}, "rewrites": []}