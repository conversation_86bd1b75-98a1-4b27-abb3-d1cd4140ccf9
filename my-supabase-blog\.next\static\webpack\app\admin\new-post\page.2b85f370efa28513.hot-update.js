"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/new-post/page",{

/***/ "(app-pages-browser)/./src/components/cherry-editor.tsx":
/*!******************************************!*\
  !*** ./src/components/cherry-editor.tsx ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CherryEditor: () => (/* binding */ CherryEditor)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ CherryEditor auto */ \nvar _s = $RefreshSig$();\n\nfunction CherryEditor(param) {\n    let { value = '', onChange, height = '500px', placeholder = '開始編寫您的文章...' } = param;\n    _s();\n    const [content, setContent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(value);\n    const handleChange = (e)=>{\n        const newValue = e.target.value;\n        setContent(newValue);\n        if (onChange) {\n            onChange(newValue);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"cherry-editor-wrapper\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n            value: content,\n            onChange: handleChange,\n            placeholder: placeholder,\n            className: \"w-full p-4 border border-input bg-background text-foreground rounded-md focus:outline-none focus:ring-2 focus:ring-ring focus:border-transparent resize-none\",\n            style: {\n                height\n            }\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test2\\\\my-supabase-blog\\\\src\\\\components\\\\cherry-editor.tsx\",\n            lineNumber: 30,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test2\\\\my-supabase-blog\\\\src\\\\components\\\\cherry-editor.tsx\",\n        lineNumber: 29,\n        columnNumber: 5\n    }, this);\n}\n_s(CherryEditor, \"qQItDjzKwGTcXbkn+HdlbdUdBc4=\");\n_c = CherryEditor;\nvar _c;\n$RefreshReg$(_c, \"CherryEditor\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/cherry-editor.tsx\n"));

/***/ })

});