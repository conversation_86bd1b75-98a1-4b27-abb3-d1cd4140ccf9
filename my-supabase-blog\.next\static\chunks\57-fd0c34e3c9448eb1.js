"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[57],{1362:(e,t,r)=>{r.d(t,{D:()=>s,N:()=>c});var n=r(2115),o=(e,t,r,n,o,u,a,l)=>{let i=document.documentElement,s=["light","dark"];function c(t){var r;(Array.isArray(e)?e:[e]).forEach(e=>{let r="class"===e,n=r&&u?o.map(e=>u[e]||e):o;r?(i.classList.remove(...n),i.classList.add(u&&u[t]?u[t]:t)):i.setAttribute(e,t)}),r=t,l&&s.includes(r)&&(i.style.colorScheme=r)}if(n)c(n);else try{let e=localStorage.getItem(t)||r,n=a&&"system"===e?window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light":e;c(n)}catch(e){}},u=["light","dark"],a="(prefers-color-scheme: dark)",l=n.createContext(void 0),i={setTheme:e=>{},themes:[]},s=()=>{var e;return null!=(e=n.useContext(l))?e:i},c=e=>n.useContext(l)?n.createElement(n.Fragment,null,e.children):n.createElement(d,{...e}),f=["light","dark"],d=e=>{let{forcedTheme:t,disableTransitionOnChange:r=!1,enableSystem:o=!0,enableColorScheme:i=!0,storageKey:s="theme",themes:c=f,defaultTheme:d=o?"system":"light",attribute:g="data-theme",value:b,children:v,nonce:E,scriptProps:P}=e,[S,T]=n.useState(()=>m(s,d)),[_,C]=n.useState(()=>"system"===S?y():S),O=b?Object.values(b):c,j=n.useCallback(e=>{let t=e;if(!t)return;"system"===e&&o&&(t=y());let n=b?b[t]:t,a=r?h(E):null,l=document.documentElement,s=e=>{"class"===e?(l.classList.remove(...O),n&&l.classList.add(n)):e.startsWith("data-")&&(n?l.setAttribute(e,n):l.removeAttribute(e))};if(Array.isArray(g)?g.forEach(s):s(g),i){let e=u.includes(d)?d:null,r=u.includes(t)?t:e;l.style.colorScheme=r}null==a||a()},[E]),w=n.useCallback(e=>{let t="function"==typeof e?e(S):e;T(t);try{localStorage.setItem(s,t)}catch(e){}},[S]),L=n.useCallback(e=>{C(y(e)),"system"===S&&o&&!t&&j("system")},[S,t]);n.useEffect(()=>{let e=window.matchMedia(a);return e.addListener(L),L(e),()=>e.removeListener(L)},[L]),n.useEffect(()=>{let e=e=>{e.key===s&&(e.newValue?T(e.newValue):w(d))};return window.addEventListener("storage",e),()=>window.removeEventListener("storage",e)},[w]),n.useEffect(()=>{j(null!=t?t:S)},[t,S]);let k=n.useMemo(()=>({theme:S,setTheme:w,forcedTheme:t,resolvedTheme:"system"===S?_:S,themes:o?[...c,"system"]:c,systemTheme:o?_:void 0}),[S,w,t,_,o,c]);return n.createElement(l.Provider,{value:k},n.createElement(p,{forcedTheme:t,storageKey:s,attribute:g,enableSystem:o,enableColorScheme:i,defaultTheme:d,value:b,themes:c,nonce:E,scriptProps:P}),v)},p=n.memo(e=>{let{forcedTheme:t,storageKey:r,attribute:u,enableSystem:a,enableColorScheme:l,defaultTheme:i,value:s,themes:c,nonce:f,scriptProps:d}=e,p=JSON.stringify([u,r,i,t,c,s,a,l]).slice(1,-1);return n.createElement("script",{...d,suppressHydrationWarning:!0,nonce:"",dangerouslySetInnerHTML:{__html:"(".concat(o.toString(),")(").concat(p,")")}})}),m=(e,t)=>{let r;try{r=localStorage.getItem(e)||void 0}catch(e){}return r||t},h=e=>{let t=document.createElement("style");return e&&t.setAttribute("nonce",e),t.appendChild(document.createTextNode("*,*::before,*::after{-webkit-transition:none!important;-moz-transition:none!important;-o-transition:none!important;-ms-transition:none!important;transition:none!important}")),document.head.appendChild(t),()=>{window.getComputedStyle(document.body),setTimeout(()=>{document.head.removeChild(t)},1)}},y=e=>(e||(e=window.matchMedia(a)),e.matches?"dark":"light")},2664:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isLocalURL",{enumerable:!0,get:function(){return u}});let n=r(9991),o=r(7102);function u(e){if(!(0,n.isAbsoluteUrl)(e))return!0;try{let t=(0,n.getLocationOrigin)(),r=new URL(e,t);return r.origin===t&&(0,o.hasBasePath)(r.pathname)}catch(e){return!1}}},2757:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{formatUrl:function(){return u},formatWithValidation:function(){return l},urlObjectKeys:function(){return a}});let n=r(6966)._(r(8859)),o=/https?|ftp|gopher|file/;function u(e){let{auth:t,hostname:r}=e,u=e.protocol||"",a=e.pathname||"",l=e.hash||"",i=e.query||"",s=!1;t=t?encodeURIComponent(t).replace(/%3A/i,":")+"@":"",e.host?s=t+e.host:r&&(s=t+(~r.indexOf(":")?"["+r+"]":r),e.port&&(s+=":"+e.port)),i&&"object"==typeof i&&(i=String(n.urlQueryToSearchParams(i)));let c=e.search||i&&"?"+i||"";return u&&!u.endsWith(":")&&(u+=":"),e.slashes||(!u||o.test(u))&&!1!==s?(s="//"+(s||""),a&&"/"!==a[0]&&(a="/"+a)):s||(s=""),l&&"#"!==l[0]&&(l="#"+l),c&&"?"!==c[0]&&(c="?"+c),""+u+s+(a=a.replace(/[?#]/g,encodeURIComponent))+(c=c.replace("#","%23"))+l}let a=["auth","hash","host","hostname","href","path","pathname","port","protocol","query","search","slashes"];function l(e){return u(e)}},3180:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"errorOnce",{enumerable:!0,get:function(){return r}});let r=e=>{}},6654:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"useMergedRef",{enumerable:!0,get:function(){return o}});let n=r(2115);function o(e,t){let r=(0,n.useRef)(null),o=(0,n.useRef)(null);return(0,n.useCallback)(n=>{if(null===n){let e=r.current;e&&(r.current=null,e());let t=o.current;t&&(o.current=null,t())}else e&&(r.current=u(e,n)),t&&(o.current=u(t,n))},[e,t])}function u(e,t){if("function"!=typeof e)return e.current=t,()=>{e.current=null};{let r=e(t);return"function"==typeof r?r:()=>e(null)}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},6874:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{default:function(){return y},useLinkStatus:function(){return b}});let n=r(6966),o=r(5155),u=n._(r(2115)),a=r(2757),l=r(5227),i=r(9818),s=r(6654),c=r(9991),f=r(5929);r(3230);let d=r(4930),p=r(2664),m=r(6634);function h(e){return"string"==typeof e?e:(0,a.formatUrl)(e)}function y(e){let t,r,n,[a,y]=(0,u.useOptimistic)(d.IDLE_LINK_STATUS),b=(0,u.useRef)(null),{href:v,as:E,children:P,prefetch:S=null,passHref:T,replace:_,shallow:C,scroll:O,onClick:j,onMouseEnter:w,onTouchStart:L,legacyBehavior:k=!1,onNavigate:A,ref:N,unstable_dynamicOnHover:M,...x}=e;t=P,k&&("string"==typeof t||"number"==typeof t)&&(t=(0,o.jsx)("a",{children:t}));let I=u.default.useContext(l.AppRouterContext),U=!1!==S,R=null===S?i.PrefetchKind.AUTO:i.PrefetchKind.FULL,{href:K,as:D}=u.default.useMemo(()=>{let e=h(v);return{href:e,as:E?h(E):e}},[v,E]);k&&(r=u.default.Children.only(t));let F=k?r&&"object"==typeof r&&r.ref:N,B=u.default.useCallback(e=>(null!==I&&(b.current=(0,d.mountLinkInstance)(e,K,I,R,U,y)),()=>{b.current&&((0,d.unmountLinkForCurrentNavigation)(b.current),b.current=null),(0,d.unmountPrefetchableInstance)(e)}),[U,K,I,R,y]),z={ref:(0,s.useMergedRef)(B,F),onClick(e){k||"function"!=typeof j||j(e),k&&r.props&&"function"==typeof r.props.onClick&&r.props.onClick(e),I&&(e.defaultPrevented||function(e,t,r,n,o,a,l){let{nodeName:i}=e.currentTarget;if(!("A"===i.toUpperCase()&&function(e){let t=e.currentTarget.getAttribute("target");return t&&"_self"!==t||e.metaKey||e.ctrlKey||e.shiftKey||e.altKey||e.nativeEvent&&2===e.nativeEvent.which}(e)||e.currentTarget.hasAttribute("download"))){if(!(0,p.isLocalURL)(t)){o&&(e.preventDefault(),location.replace(t));return}e.preventDefault(),u.default.startTransition(()=>{if(l){let e=!1;if(l({preventDefault:()=>{e=!0}}),e)return}(0,m.dispatchNavigateAction)(r||t,o?"replace":"push",null==a||a,n.current)})}}(e,K,D,b,_,O,A))},onMouseEnter(e){k||"function"!=typeof w||w(e),k&&r.props&&"function"==typeof r.props.onMouseEnter&&r.props.onMouseEnter(e),I&&U&&(0,d.onNavigationIntent)(e.currentTarget,!0===M)},onTouchStart:function(e){k||"function"!=typeof L||L(e),k&&r.props&&"function"==typeof r.props.onTouchStart&&r.props.onTouchStart(e),I&&U&&(0,d.onNavigationIntent)(e.currentTarget,!0===M)}};return(0,c.isAbsoluteUrl)(D)?z.href=D:k&&!T&&("a"!==r.type||"href"in r.props)||(z.href=(0,f.addBasePath)(D)),n=k?u.default.cloneElement(r,z):(0,o.jsx)("a",{...x,...z,children:t}),(0,o.jsx)(g.Provider,{value:a,children:n})}r(3180);let g=(0,u.createContext)(d.IDLE_LINK_STATUS),b=()=>(0,u.useContext)(g);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},8859:(e,t)=>{function r(e){let t={};for(let[r,n]of e.entries()){let e=t[r];void 0===e?t[r]=n:Array.isArray(e)?e.push(n):t[r]=[e,n]}return t}function n(e){return"string"==typeof e?e:("number"!=typeof e||isNaN(e))&&"boolean"!=typeof e?"":String(e)}function o(e){let t=new URLSearchParams;for(let[r,o]of Object.entries(e))if(Array.isArray(o))for(let e of o)t.append(r,n(e));else t.set(r,n(o));return t}function u(e){for(var t=arguments.length,r=Array(t>1?t-1:0),n=1;n<t;n++)r[n-1]=arguments[n];for(let t of r){for(let r of t.keys())e.delete(r);for(let[r,n]of t.entries())e.append(r,n)}return e}Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{assign:function(){return u},searchParamsToUrlQuery:function(){return r},urlQueryToSearchParams:function(){return o}})},9991:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{DecodeError:function(){return m},MiddlewareNotFoundError:function(){return b},MissingStaticPage:function(){return g},NormalizeError:function(){return h},PageNotFoundError:function(){return y},SP:function(){return d},ST:function(){return p},WEB_VITALS:function(){return r},execOnce:function(){return n},getDisplayName:function(){return i},getLocationOrigin:function(){return a},getURL:function(){return l},isAbsoluteUrl:function(){return u},isResSent:function(){return s},loadGetInitialProps:function(){return f},normalizeRepeatedSlashes:function(){return c},stringifyError:function(){return v}});let r=["CLS","FCP","FID","INP","LCP","TTFB"];function n(e){let t,r=!1;return function(){for(var n=arguments.length,o=Array(n),u=0;u<n;u++)o[u]=arguments[u];return r||(r=!0,t=e(...o)),t}}let o=/^[a-zA-Z][a-zA-Z\d+\-.]*?:/,u=e=>o.test(e);function a(){let{protocol:e,hostname:t,port:r}=window.location;return e+"//"+t+(r?":"+r:"")}function l(){let{href:e}=window.location,t=a();return e.substring(t.length)}function i(e){return"string"==typeof e?e:e.displayName||e.name||"Unknown"}function s(e){return e.finished||e.headersSent}function c(e){let t=e.split("?");return t[0].replace(/\\/g,"/").replace(/\/\/+/g,"/")+(t[1]?"?"+t.slice(1).join("?"):"")}async function f(e,t){let r=t.res||t.ctx&&t.ctx.res;if(!e.getInitialProps)return t.ctx&&t.Component?{pageProps:await f(t.Component,t.ctx)}:{};let n=await e.getInitialProps(t);if(r&&s(r))return n;if(!n)throw Object.defineProperty(Error('"'+i(e)+'.getInitialProps()" should resolve to an object. But found "'+n+'" instead.'),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return n}let d="undefined"!=typeof performance,p=d&&["mark","measure","getEntriesByName"].every(e=>"function"==typeof performance[e]);class m extends Error{}class h extends Error{}class y extends Error{constructor(e){super(),this.code="ENOENT",this.name="PageNotFoundError",this.message="Cannot find module for page: "+e}}class g extends Error{constructor(e,t){super(),this.message="Failed to load static file for page: "+e+" "+t}}class b extends Error{constructor(){super(),this.code="ENOENT",this.message="Cannot find the middleware module"}}function v(e){return JSON.stringify({message:e.message,stack:e.stack})}}}]);