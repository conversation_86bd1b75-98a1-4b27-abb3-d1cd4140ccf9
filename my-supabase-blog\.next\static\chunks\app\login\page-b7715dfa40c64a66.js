(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[520],{1362:(e,t,r)=>{"use strict";r.d(t,{D:()=>c,N:()=>d});var s=r(2115),n=(e,t,r,s,n,a,o,i)=>{let l=document.documentElement,c=["light","dark"];function d(t){var r;(Array.isArray(e)?e:[e]).forEach(e=>{let r="class"===e,s=r&&a?n.map(e=>a[e]||e):n;r?(l.classList.remove(...s),l.classList.add(a&&a[t]?a[t]:t)):l.setAttribute(e,t)}),r=t,i&&c.includes(r)&&(l.style.colorScheme=r)}if(s)d(s);else try{let e=localStorage.getItem(t)||r,s=o&&"system"===e?window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light":e;d(s)}catch(e){}},a=["light","dark"],o="(prefers-color-scheme: dark)",i=s.createContext(void 0),l={setTheme:e=>{},themes:[]},c=()=>{var e;return null!=(e=s.useContext(i))?e:l},d=e=>s.useContext(i)?s.createElement(s.Fragment,null,e.children):s.createElement(u,{...e}),m=["light","dark"],u=e=>{let{forcedTheme:t,disableTransitionOnChange:r=!1,enableSystem:n=!0,enableColorScheme:l=!0,storageKey:c="theme",themes:d=m,defaultTheme:u=n?"system":"light",attribute:b="data-theme",value:x,children:y,nonce:v,scriptProps:w}=e,[k,j]=s.useState(()=>f(c,u)),[N,C]=s.useState(()=>"system"===k?p():k),S=x?Object.values(x):d,E=s.useCallback(e=>{let t=e;if(!t)return;"system"===e&&n&&(t=p());let s=x?x[t]:t,o=r?g(v):null,i=document.documentElement,c=e=>{"class"===e?(i.classList.remove(...S),s&&i.classList.add(s)):e.startsWith("data-")&&(s?i.setAttribute(e,s):i.removeAttribute(e))};if(Array.isArray(b)?b.forEach(c):c(b),l){let e=a.includes(u)?u:null,r=a.includes(t)?t:e;i.style.colorScheme=r}null==o||o()},[v]),T=s.useCallback(e=>{let t="function"==typeof e?e(k):e;j(t);try{localStorage.setItem(c,t)}catch(e){}},[k]),I=s.useCallback(e=>{C(p(e)),"system"===k&&n&&!t&&E("system")},[k,t]);s.useEffect(()=>{let e=window.matchMedia(o);return e.addListener(I),I(e),()=>e.removeListener(I)},[I]),s.useEffect(()=>{let e=e=>{e.key===c&&(e.newValue?j(e.newValue):T(u))};return window.addEventListener("storage",e),()=>window.removeEventListener("storage",e)},[T]),s.useEffect(()=>{E(null!=t?t:k)},[t,k]);let A=s.useMemo(()=>({theme:k,setTheme:T,forcedTheme:t,resolvedTheme:"system"===k?N:k,themes:n?[...d,"system"]:d,systemTheme:n?N:void 0}),[k,T,t,N,n,d]);return s.createElement(i.Provider,{value:A},s.createElement(h,{forcedTheme:t,storageKey:c,attribute:b,enableSystem:n,enableColorScheme:l,defaultTheme:u,value:x,themes:d,nonce:v,scriptProps:w}),y)},h=s.memo(e=>{let{forcedTheme:t,storageKey:r,attribute:a,enableSystem:o,enableColorScheme:i,defaultTheme:l,value:c,themes:d,nonce:m,scriptProps:u}=e,h=JSON.stringify([a,r,l,t,d,c,o,i]).slice(1,-1);return s.createElement("script",{...u,suppressHydrationWarning:!0,nonce:"",dangerouslySetInnerHTML:{__html:"(".concat(n.toString(),")(").concat(h,")")}})}),f=(e,t)=>{let r;try{r=localStorage.getItem(e)||void 0}catch(e){}return r||t},g=e=>{let t=document.createElement("style");return e&&t.setAttribute("nonce",e),t.appendChild(document.createTextNode("*,*::before,*::after{-webkit-transition:none!important;-moz-transition:none!important;-o-transition:none!important;-ms-transition:none!important;transition:none!important}")),document.head.appendChild(t),()=>{window.getComputedStyle(document.body),setTimeout(()=>{document.head.removeChild(t)},1)}},p=e=>(e||(e=window.matchMedia(o)),e.matches?"dark":"light")},2643:(e,t,r)=>{"use strict";r.d(t,{U:()=>n});var s=r(3865);function n(){return(0,s.createBrowserClient)("https://sqoixvgmroejgaebxyeq.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InNxb2l4dmdtcm9lamdhZWJ4eWVxIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTExNjU0ODksImV4cCI6MjA2Njc0MTQ4OX0.SFkF9v0Y-lrJuQHiSf5HLTLuwgOXRf0ERQKnfQNfLsU")}},3430:(e,t,r)=>{Promise.resolve().then(r.bind(r,9690))},6685:(e,t,r)=>{"use strict";r.d(t,{ThemeToggle:()=>c});var s=r(5155),n=r(2115),a=r(9946);let o=(0,a.A)("sun",[["circle",{cx:"12",cy:"12",r:"4",key:"4exip2"}],["path",{d:"M12 2v2",key:"tus03m"}],["path",{d:"M12 20v2",key:"1lh1kg"}],["path",{d:"m4.93 4.93 1.41 1.41",key:"149t6j"}],["path",{d:"m17.66 17.66 1.41 1.41",key:"ptbguv"}],["path",{d:"M2 12h2",key:"1t8f8n"}],["path",{d:"M20 12h2",key:"1q8mjw"}],["path",{d:"m6.34 17.66-1.41 1.41",key:"1m8zz5"}],["path",{d:"m19.07 4.93-1.41 1.41",key:"1shlcs"}]]),i=(0,a.A)("moon",[["path",{d:"M12 3a6 6 0 0 0 9 9 9 9 0 1 1-9-9Z",key:"a7tn18"}]]);var l=r(1362);function c(){let{theme:e,setTheme:t}=(0,l.D)(),[r,a]=n.useState(!1);return(n.useEffect(()=>{a(!0)},[]),r)?(0,s.jsxs)("button",{onClick:()=>t("light"===e?"dark":"light"),className:"inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none ring-offset-background border border-input bg-background hover:bg-accent hover:text-accent-foreground h-10 w-10",children:["light"===e?(0,s.jsx)(i,{className:"h-[1.2rem] w-[1.2rem]"}):(0,s.jsx)(o,{className:"h-[1.2rem] w-[1.2rem]"}),(0,s.jsx)("span",{className:"sr-only",children:"切換主題"})]}):(0,s.jsxs)("button",{className:"inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none ring-offset-background border border-input bg-background hover:bg-accent hover:text-accent-foreground h-10 w-10",children:[(0,s.jsx)(o,{className:"h-[1.2rem] w-[1.2rem]"}),(0,s.jsx)("span",{className:"sr-only",children:"切換主題"})]})}},9690:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>d});var s=r(5155),n=r(2115),a=r(2643),o=r(5695),i=r(6874),l=r.n(i),c=r(6685);function d(){let[e,t]=(0,n.useState)(""),[r,i]=(0,n.useState)(""),[d,m]=(0,n.useState)(!1),[u,h]=(0,n.useState)(""),[f,g]=(0,n.useState)(!1),p=(0,o.useRouter)(),b=(0,a.U)(),x=async t=>{t.preventDefault(),m(!0),h("");try{if(f){let{error:t}=await b.auth.signUp({email:e,password:r});if(t)throw t;h("註冊成功！請檢查您的電子郵件以驗證帳戶。")}else{let{error:t}=await b.auth.signInWithPassword({email:e,password:r});if(t)throw t;p.push("/"),p.refresh()}}catch(e){h(e instanceof Error?e.message:"發生錯誤")}finally{m(!1)}};return(0,s.jsxs)("div",{className:"min-h-screen bg-background",children:[(0,s.jsx)("header",{className:"bg-card shadow-sm border-b border-border",children:(0,s.jsx)("div",{className:"max-w-4xl mx-auto px-4 py-6",children:(0,s.jsxs)("div",{className:"flex justify-between items-center",children:[(0,s.jsx)(l(),{href:"/",className:"text-2xl font-bold text-foreground hover:text-primary transition-colors",children:"我的部落格"}),(0,s.jsx)(c.ThemeToggle,{})]})})}),(0,s.jsx)("div",{className:"flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8",children:(0,s.jsxs)("div",{className:"max-w-md w-full space-y-8",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h2",{className:"mt-6 text-center text-3xl font-extrabold text-foreground",children:f?"建立新帳戶":"登入您的帳戶"}),(0,s.jsxs)("p",{className:"mt-2 text-center text-sm text-muted-foreground",children:["或"," ",(0,s.jsx)(l(),{href:"/",className:"font-medium text-primary hover:text-primary/80",children:"返回首頁"})]})]}),(0,s.jsx)("div",{className:"bg-card rounded-lg shadow-sm border border-border p-8",children:(0,s.jsxs)("form",{className:"space-y-6",onSubmit:x,children:[(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"email",className:"block text-sm font-medium text-foreground mb-2",children:"電子郵件"}),(0,s.jsx)("input",{id:"email",name:"email",type:"email",autoComplete:"email",required:!0,className:"w-full px-3 py-2 border border-input bg-background text-foreground rounded-md focus:outline-none focus:ring-2 focus:ring-ring focus:border-transparent",placeholder:"輸入您的電子郵件地址",value:e,onChange:e=>t(e.target.value)})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"password",className:"block text-sm font-medium text-foreground mb-2",children:"密碼"}),(0,s.jsx)("input",{id:"password",name:"password",type:"password",autoComplete:"current-password",required:!0,className:"w-full px-3 py-2 border border-input bg-background text-foreground rounded-md focus:outline-none focus:ring-2 focus:ring-ring focus:border-transparent",placeholder:"輸入您的密碼",value:r,onChange:e=>i(e.target.value)})]})]}),u&&(0,s.jsx)("div",{className:"text-sm text-center p-3 rounded-md ".concat(u.includes("成功")?"bg-green-50 text-green-700 border border-green-200 dark:bg-green-900/20 dark:text-green-400 dark:border-green-800":"bg-destructive/10 text-destructive border border-destructive/20"),children:u}),(0,s.jsx)("div",{children:(0,s.jsx)("button",{type:"submit",disabled:d,className:"w-full bg-primary text-primary-foreground py-2 px-4 rounded-md hover:bg-primary/90 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors",children:d?"處理中...":f?"註冊":"登入"})}),(0,s.jsx)("div",{className:"text-center",children:(0,s.jsx)("button",{type:"button",onClick:()=>g(!f),className:"text-primary hover:text-primary/80 text-sm transition-colors",children:f?"已有帳戶？點此登入":"沒有帳戶？點此註冊"})})]})})]})})]})}},9946:(e,t,r)=>{"use strict";r.d(t,{A:()=>m});var s=r(2115);let n=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),a=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,r)=>r?r.toUpperCase():t.toLowerCase()),o=e=>{let t=a(e);return t.charAt(0).toUpperCase()+t.slice(1)},i=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return t.filter((e,t,r)=>!!e&&""!==e.trim()&&r.indexOf(e)===t).join(" ").trim()},l=e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0};var c={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let d=(0,s.forwardRef)((e,t)=>{let{color:r="currentColor",size:n=24,strokeWidth:a=2,absoluteStrokeWidth:o,className:d="",children:m,iconNode:u,...h}=e;return(0,s.createElement)("svg",{ref:t,...c,width:n,height:n,stroke:r,strokeWidth:o?24*Number(a)/Number(n):a,className:i("lucide",d),...!m&&!l(h)&&{"aria-hidden":"true"},...h},[...u.map(e=>{let[t,r]=e;return(0,s.createElement)(t,r)}),...Array.isArray(m)?m:[m]])}),m=(e,t)=>{let r=(0,s.forwardRef)((r,a)=>{let{className:l,...c}=r;return(0,s.createElement)(d,{ref:a,iconNode:t,className:i("lucide-".concat(n(o(e))),"lucide-".concat(e),l),...c})});return r.displayName=o(e),r}}},e=>{var t=t=>e(e.s=t);e.O(0,[874,271,441,684,358],()=>t(3430)),_N_E=e.O()}]);