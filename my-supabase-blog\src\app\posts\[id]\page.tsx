import { notFound } from 'next/navigation'
import Link from 'next/link'
import { createClient } from '@/lib/supabase/server'
import { getCurrentUser, isAdmin } from '@/lib/auth'
import { Post } from '@/types/database'
import { MarkdownRenderer } from '@/components/markdown-renderer'
import { ClientThemeToggle } from '@/components/client-theme-toggle'
import { AdminActions } from '@/components/admin-actions' // 新增导入

async function getPost(id: string): Promise<Post | null> {
  const supabase = await createClient()
  const { data: post, error } = await supabase
    .from('posts')
    .select('*')
    .eq('id', id)
    .single()

  if (error) {
    console.error('Error fetching post:', error)
    return null
  }

  return post
}

export default async function PostPage({ params }: { params: { id: string } }) {
  const { id } = params
  const post = await getPost(id)
  
  if (!post) {
    notFound()
  }

  const user = await getCurrentUser()
  const userIsAdmin = user ? await isAdmin(user.id) : false

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <header className="bg-card shadow-sm border-b border-border">
        <div className="max-w-4xl mx-auto px-4 py-6">
          <div className="flex justify-between items-center">
            <Link 
              href="/"
              className="text-2xl font-bold text-foreground hover:text-primary transition-colors"
            >
              我的部落格
            </Link>
            <div className="flex items-center gap-4">
              <ClientThemeToggle />
              {user ? (
                <>
                  <span className="text-sm text-muted-foreground">
                    歡迎, {user.email}
                  </span>
                  {userIsAdmin && (
                    <>
                      <Link
                        href="/admin/new-post"
                        className="bg-primary text-primary-foreground px-4 py-2 rounded-md hover:bg-primary/90 transition-colors"
                      >
                        新增文章
                      </Link>
                      <Link
                        href="/admin/manage-posts"
                        className="bg-secondary text-secondary-foreground px-4 py-2 rounded-md hover:bg-secondary/80 transition-colors"
                      >
                        管理文章
                      </Link>
                    </>
                  )}
                  <form action="/auth/signout" method="post">
                    <button
                      type="submit"
                      className="text-muted-foreground hover:text-foreground transition-colors"
                    >
                      登出
                    </button>
                  </form>
                </>
              ) : (
                <Link
                  href="/login"
                  className="bg-primary text-primary-foreground px-4 py-2 rounded-md hover:bg-primary/90 transition-colors"
                >
                  登入
                </Link>
              )}
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-4xl mx-auto px-4 py-8">
        {/* 返回按鈕 */}
        <div className="mb-6">
          <Link
            href="/"
            className="inline-flex items-center text-muted-foreground hover:text-foreground transition-colors"
          >
            ← 返回文章列表
          </Link>
        </div>

        {/* 文章內容 */}
        <article className="bg-card rounded-lg shadow-sm border border-border p-8">
          {/* 文章標題 */}
          <header className="mb-8">
            <h1 className="text-4xl font-bold text-card-foreground mb-4">
              {post.title}
            </h1>
            <div className="flex items-center gap-4 text-sm text-muted-foreground">
              <time>
                發布於 {new Date(post.created_at).toLocaleDateString('zh-TW', {
                  year: 'numeric',
                  month: 'long',
                  day: 'numeric'
                })}
              </time>
              {post.updated_at !== post.created_at && (
                <span>
                  更新於 {new Date(post.updated_at).toLocaleDateString('zh-TW', {
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric'
                  })}
                </span>
              )}
            </div>
          </header>

          {/* 文章內容 */}
          <div className="prose prose-lg max-w-none dark:prose-invert">
            <MarkdownRenderer content={post.content} />
          </div>
        </article>

        {/* 管理員操作 */}
        {userIsAdmin && (
          <div className="mt-8 p-4 bg-muted rounded-lg">
            <h3 className="text-lg font-semibold text-foreground mb-4">管理員操作</h3>
            <AdminActions postId={post.id} />
          </div>
        )}
      </main>
    </div>
  )
}
