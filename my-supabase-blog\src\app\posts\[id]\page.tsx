import { notFound } from 'next/navigation'
import Link from 'next/link'
import { createClient } from '@/lib/supabase/server'
import { getCurrentUser, isAdmin } from '@/lib/auth'
import { Post } from '@/types/database'
import { MarkdownRenderer } from '@/components/markdown-renderer'
import { ClientThemeToggle } from '@/components/client-theme-toggle'
import { AdminActions } from '@/components/admin-actions' 

async function getPost(id: string): Promise<Post | null> {
  const supabase = await createClient()
  const { data: post, error } = await supabase
    .from('posts')
    .select('*')
    .eq('id', id)
    .single()

  if (error) {
    console.error('Error fetching post:', error)
    return null
  }

  return post
}

export default async function PostPage({ params }: { params: { id: string } }) {
  const { id } = params
  const post = await getPost(id)
  
  if (!post) {
    notFound()
  }

  const user = await getCurrentUser()
  const userIsAdmin = user ? await isAdmin(user.id) : false

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <header className="bg-card/95 shadow-sm border-b border-border sticky top-0 z-50 backdrop-blur-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4 lg:py-6">
          <div className="flex justify-between items-center">
            <Link
              href="/"
              className="text-xl sm:text-2xl font-bold text-foreground hover:text-primary transition-colors"
            >
              My Blog
            </Link>
            <div className="flex items-center gap-2 sm:gap-4">
              <ClientThemeToggle />
              {user ? (
                <>
                  <span className="hidden sm:block text-sm text-muted-foreground">
                    Welcome, {user.email}
                  </span>
                  {userIsAdmin && (
                    <>
                      <Link
                        href="/admin/new-post"
                        className="bg-primary text-primary-foreground px-3 sm:px-4 py-2 rounded-md hover:bg-primary/90 transition-all duration-200 text-sm font-medium shadow-sm hover:shadow-md"
                      >
                        <span className="hidden sm:inline">New Post</span>
                        <span className="sm:hidden">+</span>
                      </Link>
                      <Link
                        href="/admin/manage-posts"
                        className="bg-secondary text-secondary-foreground px-3 sm:px-4 py-2 rounded-md hover:bg-secondary/80 transition-all duration-200 text-sm font-medium shadow-sm hover:shadow-md"
                      >
                        <span className="hidden sm:inline">Manage</span>
                        <span className="sm:hidden">⚙️</span>
                      </Link>
                    </>
                  )}
                  <form action="/auth/signout" method="post">
                    <button
                      type="submit"
                      className="text-muted-foreground hover:text-foreground transition-colors text-sm"
                    >
                      <span className="hidden sm:inline">Sign Out</span>
                      <span className="sm:hidden">↗️</span>
                    </button>
                  </form>
                </>
              ) : (
                <Link
                  href="/login"
                  className="bg-primary text-primary-foreground px-3 sm:px-4 py-2 rounded-md hover:bg-primary/90 transition-all duration-200 text-sm font-medium shadow-sm hover:shadow-md"
                >
                  Sign In
                </Link>
              )}
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8 lg:py-12">
        {/* Back Button */}
        <div className="mb-8">
          <Link
            href="/"
            className="inline-flex items-center text-muted-foreground hover:text-foreground transition-all duration-200 group"
          >
            <svg className="w-4 h-4 mr-2 group-hover:-translate-x-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
            </svg>
            Back to posts
          </Link>
        </div>

        {/* Article Content */}
        <article className="bg-card rounded-xl shadow-lg border border-border overflow-hidden">
          {/* Article Header */}
          <header className="p-6 sm:p-8 lg:p-10 border-b border-border bg-gradient-to-r from-primary/5 to-secondary/5">
            <h1 className="text-2xl sm:text-3xl lg:text-4xl font-bold text-card-foreground mb-6 leading-tight">
              {post.title}
            </h1>
            <div className="flex flex-col sm:flex-row sm:items-center gap-4 text-sm text-muted-foreground">
              <time className="flex items-center">
                <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                </svg>
                Published on {new Date(post.created_at).toLocaleDateString('en-US', {
                  year: 'numeric',
                  month: 'long',
                  day: 'numeric'
                })}
              </time>
              {post.updated_at !== post.created_at && (
                <span className="flex items-center">
                  <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                  </svg>
                  Updated on {new Date(post.updated_at).toLocaleDateString('en-US', {
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric'
                  })}
                </span>
              )}
            </div>
          </header>

          {/* Article Body */}
          <div className="p-6 sm:p-8 lg:p-10">
            <div className="prose prose-lg max-w-none dark:prose-invert prose-headings:text-foreground prose-p:text-muted-foreground prose-strong:text-foreground prose-code:text-foreground prose-pre:bg-muted prose-pre:border prose-pre:border-border">
              <MarkdownRenderer content={post.content} />
            </div>
          </div>
        </article>

        {/* Admin Actions */}
        {userIsAdmin && (
          <div className="mt-8 p-6 bg-muted/50 rounded-xl border border-border">
            <h3 className="text-lg font-semibold text-foreground mb-4 flex items-center">
              <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
              </svg>
              Admin Actions
            </h3>
            <AdminActions postId={post.id} />
          </div>
        )}
      </main>
    </div>
  )
}
