{"version": 3, "sources": [], "sections": [{"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test2/my-supabase-blog/src/components/markdown-renderer.tsx"], "sourcesContent": ["'use client'\n\nimport dynamic from 'next/dynamic'\nimport { useState, useEffect } from 'react'\nimport '@uiw/react-markdown-preview/markdown.css'\n\n// Dynamically import MDEditor.Markdown to avoid SSR issues\nconst MDPreview = dynamic(\n  () => import('@uiw/react-md-editor').then(mod => ({ default: mod.default.Markdown })),\n  { ssr: false }\n)\n\ninterface MarkdownRendererProps {\n  content: string\n  className?: string\n}\n\nexport function MarkdownRenderer({ content, className = '' }: MarkdownRendererProps) {\n  const [mounted, setMounted] = useState(false)\n\n  useEffect(() => {\n    setMounted(true)\n  }, [])\n\n  // Show loading state during SSR or fallback to simple rendering\n  if (!mounted) {\n    const renderContent = (text: string) => {\n      return text\n        .split('\\n')\n        .map((line, index) => (\n          <div key={index} className=\"mb-2\">\n            {line || <br />}\n          </div>\n        ))\n    }\n\n    return (\n      <div className={`markdown-renderer ${className}`}>\n        <div className=\"prose prose-lg max-w-none dark:prose-invert text-foreground\">\n          {renderContent(content)}\n        </div>\n      </div>\n    )\n  }\n\n  return (\n    <div className={`markdown-renderer ${className}`} data-color-mode=\"auto\">\n      <MDPreview\n        source={content}\n        style={{\n          whiteSpace: 'pre-wrap',\n          backgroundColor: 'transparent',\n          color: 'inherit'\n        }}\n      />\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;;AAHA;;;;;AAMA,2DAA2D;AAC3D,MAAM,YAAY,CAAA,GAAA,+JAAA,CAAA,UAAO,AAAD;;;;;;IAEpB,KAAK;;AAQF,SAAS,iBAAiB,EAAE,OAAO,EAAE,YAAY,EAAE,EAAyB;IACjF,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,WAAW;IACb,GAAG,EAAE;IAEL,gEAAgE;IAChE,IAAI,CAAC,SAAS;QACZ,MAAM,gBAAgB,CAAC;YACrB,OAAO,KACJ,KAAK,CAAC,MACN,GAAG,CAAC,CAAC,MAAM,sBACV,8OAAC;oBAAgB,WAAU;8BACxB,sBAAQ,8OAAC;;;;;mBADF;;;;;QAIhB;QAEA,qBACE,8OAAC;YAAI,WAAW,CAAC,kBAAkB,EAAE,WAAW;sBAC9C,cAAA,8OAAC;gBAAI,WAAU;0BACZ,cAAc;;;;;;;;;;;IAIvB;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAC,kBAAkB,EAAE,WAAW;QAAE,mBAAgB;kBAChE,cAAA,8OAAC;YACC,QAAQ;YACR,OAAO;gBACL,YAAY;gBACZ,iBAAiB;gBACjB,OAAO;YACT;;;;;;;;;;;AAIR", "debugId": null}}, {"offset": {"line": 116, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test2/my-supabase-blog/src/components/theme-toggle.tsx"], "sourcesContent": ["'use client'\n\nimport * as React from 'react'\nimport { Moon, Sun } from 'lucide-react'\nimport { useTheme } from 'next-themes'\n\nexport function ThemeToggle() {\n  const { theme, setTheme } = useTheme()\n  const [mounted, setMounted] = React.useState(false)\n\n  React.useEffect(() => {\n    setMounted(true)\n  }, [])\n\n  if (!mounted) {\n    return (\n      <button className=\"inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none ring-offset-background border border-input bg-background hover:bg-accent hover:text-accent-foreground h-10 w-10\">\n        <Sun className=\"h-[1.2rem] w-[1.2rem]\" />\n        <span className=\"sr-only\">切換主題</span>\n      </button>\n    )\n  }\n\n  return (\n    <button\n      onClick={() => setTheme(theme === 'light' ? 'dark' : 'light')}\n      className=\"inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none ring-offset-background border border-input bg-background hover:bg-accent hover:text-accent-foreground h-10 w-10\"\n    >\n      {theme === 'light' ? (\n        <Moon className=\"h-[1.2rem] w-[1.2rem]\" />\n      ) : (\n        <Sun className=\"h-[1.2rem] w-[1.2rem]\" />\n      )}\n      <span className=\"sr-only\">切換主題</span>\n    </button>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAJA;;;;;AAMO,SAAS;IACd,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,gJAAA,CAAA,WAAQ,AAAD;IACnC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAc,AAAD,EAAE;IAE7C,CAAA,GAAA,qMAAA,CAAA,YAAe,AAAD,EAAE;QACd,WAAW;IACb,GAAG,EAAE;IAEL,IAAI,CAAC,SAAS;QACZ,qBACE,8OAAC;YAAO,WAAU;;8BAChB,8OAAC,gMAAA,CAAA,MAAG;oBAAC,WAAU;;;;;;8BACf,8OAAC;oBAAK,WAAU;8BAAU;;;;;;;;;;;;IAGhC;IAEA,qBACE,8OAAC;QACC,SAAS,IAAM,SAAS,UAAU,UAAU,SAAS;QACrD,WAAU;;YAET,UAAU,wBACT,8OAAC,kMAAA,CAAA,OAAI;gBAAC,WAAU;;;;;qCAEhB,8OAAC,gMAAA,CAAA,MAAG;gBAAC,WAAU;;;;;;0BAEjB,8OAAC;gBAAK,WAAU;0BAAU;;;;;;;;;;;;AAGhC", "debugId": null}}]}