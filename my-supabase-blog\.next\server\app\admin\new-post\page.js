(()=>{var e={};e.id=936,e.ids=[936],e.modules={173:(e,t,r)=>{"use strict";r.d(t,{default:()=>s});let s=(0,r(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test2\\\\my-supabase-blog\\\\src\\\\app\\\\admin\\\\new-post\\\\new-post-form.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\test2\\my-supabase-blog\\src\\app\\admin\\new-post\\new-post-form.tsx","default")},232:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});var s=r(7413),o=r(9916),n=r(2909),i=r(173);async function a(){let e=await (0,n.HW)();return e||(0,o.redirect)("/login"),await (0,n.qc)(e.id)||(0,o.redirect)("/"),(0,s.jsx)("div",{className:"min-h-screen bg-background",children:(0,s.jsxs)("div",{className:"max-w-4xl mx-auto px-4 py-8",children:[(0,s.jsxs)("div",{className:"mb-8",children:[(0,s.jsx)("h1",{className:"text-3xl font-bold text-foreground mb-2",children:"新增文章"}),(0,s.jsx)("p",{className:"text-muted-foreground",children:"使用 Markdown 編輯器建立新的部落格文章"})]}),(0,s.jsx)(i.default,{})]})})}},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1365:(e,t,r)=>{"use strict";r.d(t,{D:()=>i});var s=r(687),o=r(3210),n=r(218);function i({value:e="",onChange:t,height:r="500px",placeholder:i="開始編寫您的文章..."}){let a=(0,o.useRef)(null);(0,o.useRef)(null);let[d,l]=(0,o.useState)(!1),{theme:u}=(0,n.D)();return(0,s.jsxs)("div",{className:"cherry-editor-wrapper",children:[(0,s.jsx)("div",{ref:a,className:"cherry-editor",style:{minHeight:r}}),!d&&(0,s.jsx)("div",{className:"flex items-center justify-center h-64 bg-muted rounded-md",children:(0,s.jsx)("div",{className:"text-muted-foreground",children:"載入編輯器中..."})})]})}},1630:e=>{"use strict";e.exports=require("http")},1645:e=>{"use strict";e.exports=require("net")},1747:(e,t,r)=>{"use strict";r.d(t,{default:()=>u});var s=r(687),o=r(3210),n=r(6189),i=r(5814),a=r.n(i),d=r(1365),l=r(9481);function u(){let[e,t]=(0,o.useState)(""),[r,i]=(0,o.useState)(""),[u,c]=(0,o.useState)(!1),[p,m]=(0,o.useState)(""),x=(0,n.useRouter)(),h=async t=>{if(t.preventDefault(),!e.trim()||!r.trim())return void m("標題和內容都是必填的");c(!0),m("");try{let t=(0,l.U)(),{error:s}=await t.from("posts").insert([{title:e.trim(),content:r.trim()}]);if(s)throw s;x.push("/"),x.refresh()}catch(e){console.error("Error creating post:",e),m("建立文章時發生錯誤，請稍後再試")}finally{c(!1)}};return(0,s.jsxs)("form",{onSubmit:h,className:"space-y-6",children:[p&&(0,s.jsx)("div",{className:"bg-destructive/10 border border-destructive/20 text-destructive px-4 py-3 rounded-md",children:p}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"title",className:"block text-sm font-medium text-foreground mb-2",children:"文章標題"}),(0,s.jsx)("input",{type:"text",id:"title",value:e,onChange:e=>t(e.target.value),className:"w-full px-3 py-2 border border-input bg-background text-foreground rounded-md focus:outline-none focus:ring-2 focus:ring-ring focus:border-transparent",placeholder:"輸入文章標題...",disabled:u})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"content",className:"block text-sm font-medium text-foreground mb-2",children:"文章內容"}),(0,s.jsx)("div",{className:"border border-input rounded-md overflow-hidden",children:(0,s.jsx)(d.D,{value:r,onChange:i,height:"600px",placeholder:"開始編寫您的文章內容..."})})]}),(0,s.jsxs)("div",{className:"flex items-center gap-4",children:[(0,s.jsx)("button",{type:"submit",disabled:u||!e.trim()||!r.trim(),className:"bg-primary text-primary-foreground px-6 py-2 rounded-md hover:bg-primary/90 disabled:opacity-50 disabled:cursor-not-allowed transition-colors",children:u?"發布中...":"發布文章"}),(0,s.jsx)(a(),{href:"/",className:"bg-secondary text-secondary-foreground px-6 py-2 rounded-md hover:bg-secondary/80 transition-colors",children:"取消"})]})]})}},1997:e=>{"use strict";e.exports=require("punycode")},2831:(e,t,r)=>{Promise.resolve().then(r.bind(r,173))},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3873:e=>{"use strict";e.exports=require("path")},4075:e=>{"use strict";e.exports=require("zlib")},4631:e=>{"use strict";e.exports=require("tls")},4735:e=>{"use strict";e.exports=require("events")},5511:e=>{"use strict";e.exports=require("crypto")},5591:e=>{"use strict";e.exports=require("https")},7910:e=>{"use strict";e.exports=require("stream")},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9428:e=>{"use strict";e.exports=require("buffer")},9434:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.a,__next_app__:()=>c,pages:()=>u,routeModule:()=>p,tree:()=>l});var s=r(5239),o=r(8088),n=r(8170),i=r.n(n),a=r(893),d={};for(let e in a)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>a[e]);r.d(t,d);let l={children:["",{children:["admin",{children:["new-post",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,232)),"C:\\Users\\<USER>\\Desktop\\test2\\my-supabase-blog\\src\\app\\admin\\new-post\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,4431)),"C:\\Users\\<USER>\\Desktop\\test2\\my-supabase-blog\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,5284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,u=["C:\\Users\\<USER>\\Desktop\\test2\\my-supabase-blog\\src\\app\\admin\\new-post\\page.tsx"],c={require:r,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:o.RouteKind.APP_PAGE,page:"/admin/new-post/page",pathname:"/admin/new-post",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},9481:(e,t,r)=>{"use strict";r.d(t,{U:()=>o});var s=r(9522);function o(){return(0,s.createBrowserClient)("https://sqoixvgmroejgaebxyeq.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InNxb2l4dmdtcm9lamdhZWJ4eWVxIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTExNjU0ODksImV4cCI6MjA2Njc0MTQ4OX0.SFkF9v0Y-lrJuQHiSf5HLTLuwgOXRf0ERQKnfQNfLsU")}},9551:e=>{"use strict";e.exports=require("url")},9783:(e,t,r)=>{Promise.resolve().then(r.bind(r,1747))}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[447,98,866,567,318,924],()=>r(9434));module.exports=s})();