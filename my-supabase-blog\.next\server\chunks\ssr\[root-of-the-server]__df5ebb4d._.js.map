{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 104, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test2/my-supabase-blog/src/lib/supabase/server.ts"], "sourcesContent": ["import { createServerClient } from '@supabase/ssr'\nimport { cookies } from 'next/headers'\n\nexport async function createClient() {\n  const cookieStore = await cookies()\n\n  return createServerClient(\n    process.env.NEXT_PUBLIC_SUPABASE_URL!,\n    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,\n    {\n      cookies: {\n        getAll() {\n          return cookieStore.getAll()\n        },\n        setAll(cookiesToSet) {\n          try {\n            cookiesToSet.forEach(({ name, value, options }) =>\n              cookieStore.set(name, value, options)\n            )\n          } catch {\n            // The `setAll` method was called from a Server Component.\n            // This can be ignored if you have middleware refreshing\n            // user sessions.\n          }\n        },\n      },\n    }\n  )\n}\n"], "names": [], "mappings": ";;;AAAA;AAAA;AACA;;;AAEO,eAAe;IACpB,MAAM,cAAc,MAAM,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAEhC,OAAO,CAAA,GAAA,yKAAA,CAAA,qBAAkB,AAAD,sUAGtB;QACE,SAAS;YACP;gBACE,OAAO,YAAY,MAAM;YAC3B;YACA,QAAO,YAAY;gBACjB,IAAI;oBACF,aAAa,OAAO,CAAC,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,OAAO,EAAE,GAC5C,YAAY,GAAG,CAAC,MAAM,OAAO;gBAEjC,EAAE,OAAM;gBACN,0DAA0D;gBAC1D,wDAAwD;gBACxD,iBAAiB;gBACnB;YACF;QACF;IACF;AAEJ", "debugId": null}}, {"offset": {"line": 137, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test2/my-supabase-blog/src/lib/auth.ts"], "sourcesContent": ["import { createClient } from '@/lib/supabase/server'\n\nexport async function getCurrentUser() {\n  const supabase = await createClient()\n  const { data: { user } } = await supabase.auth.getUser()\n  return user\n}\n\nexport async function isAdmin(userId?: string) {\n  if (!userId) return false\n\n  const supabase = await createClient()\n\n  // 方法1: 檢查 profiles 表中的 is_admin 欄位\n  const { data: profile } = await supabase\n    .from('profiles')\n    .select('is_admin')\n    .eq('id', userId)\n    .single()\n\n  if (profile?.is_admin) return true\n\n  // 方法2: 檢查是否為預設管理員 email\n  const { data: { user } } = await supabase.auth.getUser()\n  const adminEmail = process.env.ADMIN_EMAIL\n\n  return user?.email === adminEmail\n}\n\nexport async function requireAdmin() {\n  const user = await getCurrentUser()\n  if (!user) {\n    throw new Error('未登入')\n  }\n\n  const isUserAdmin = await isAdmin(user.id)\n  if (!isUserAdmin) {\n    throw new Error('需要管理員權限')\n  }\n\n  return user\n}\n"], "names": [], "mappings": ";;;;;AAAA;;AAEO,eAAe;IACpB,MAAM,WAAW,MAAM,CAAA,GAAA,gIAAA,CAAA,eAAY,AAAD;IAClC,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;IACtD,OAAO;AACT;AAEO,eAAe,QAAQ,MAAe;IAC3C,IAAI,CAAC,QAAQ,OAAO;IAEpB,MAAM,WAAW,MAAM,CAAA,GAAA,gIAAA,CAAA,eAAY,AAAD;IAElC,mCAAmC;IACnC,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,MAAM,SAC7B,IAAI,CAAC,YACL,MAAM,CAAC,YACP,EAAE,CAAC,MAAM,QACT,MAAM;IAET,IAAI,SAAS,UAAU,OAAO;IAE9B,wBAAwB;IACxB,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;IACtD,MAAM,aAAa,QAAQ,GAAG,CAAC,WAAW;IAE1C,OAAO,MAAM,UAAU;AACzB;AAEO,eAAe;IACpB,MAAM,OAAO,MAAM;IACnB,IAAI,CAAC,MAAM;QACT,MAAM,IAAI,MAAM;IAClB;IAEA,MAAM,cAAc,MAAM,QAAQ,KAAK,EAAE;IACzC,IAAI,CAAC,aAAa;QAChB,MAAM,IAAI,MAAM;IAClB;IAEA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 177, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test2/my-supabase-blog/src/components/markdown-renderer.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const MarkdownRenderer = registerClientReference(\n    function() { throw new Error(\"Attempted to call MarkdownRenderer() from the server but <PERSON><PERSON><PERSON><PERSON><PERSON> is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/markdown-renderer.tsx <module evaluation>\",\n    \"MarkdownRenderer\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,mBAAmB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAClD;IAAa,MAAM,IAAI,MAAM;AAAgP,GAC7Q,sEACA", "debugId": null}}, {"offset": {"line": 191, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test2/my-supabase-blog/src/components/markdown-renderer.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const MarkdownRenderer = registerClientReference(\n    function() { throw new Error(\"Attempted to call MarkdownRenderer() from the server but <PERSON><PERSON><PERSON><PERSON><PERSON> is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/markdown-renderer.tsx\",\n    \"MarkdownRenderer\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,mBAAmB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAClD;IAAa,MAAM,IAAI,MAAM;AAAgP,GAC7Q,kDACA", "debugId": null}}, {"offset": {"line": 205, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 215, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test2/my-supabase-blog/src/components/client-theme-toggle.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const ClientThemeToggle = registerClientReference(\n    function() { throw new Error(\"Attempted to call ClientThemeToggle() from the server but ClientThemeToggle is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/client-theme-toggle.tsx <module evaluation>\",\n    \"ClientThemeToggle\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,oBAAoB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACnD;IAAa,MAAM,IAAI,MAAM;AAAkP,GAC/Q,wEACA", "debugId": null}}, {"offset": {"line": 229, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test2/my-supabase-blog/src/components/client-theme-toggle.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const ClientThemeToggle = registerClientReference(\n    function() { throw new Error(\"Attempted to call ClientThemeToggle() from the server but ClientThemeToggle is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/client-theme-toggle.tsx\",\n    \"ClientThemeToggle\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,oBAAoB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACnD;IAAa,MAAM,IAAI,MAAM;AAAkP,GAC/Q,oDACA", "debugId": null}}, {"offset": {"line": 243, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 253, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test2/my-supabase-blog/src/components/admin-actions.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const AdminActions = registerClientReference(\n    function() { throw new Error(\"Attempted to call AdminActions() from the server but AdminActions is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/admin-actions.tsx <module evaluation>\",\n    \"AdminActions\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,eAAe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC9C;IAAa,MAAM,IAAI,MAAM;AAAwO,GACrQ,kEACA", "debugId": null}}, {"offset": {"line": 267, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test2/my-supabase-blog/src/components/admin-actions.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const AdminActions = registerClientReference(\n    function() { throw new Error(\"Attempted to call AdminActions() from the server but AdminActions is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/admin-actions.tsx\",\n    \"AdminActions\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,eAAe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC9C;IAAa,MAAM,IAAI,MAAM;AAAwO,GACrQ,8CACA", "debugId": null}}, {"offset": {"line": 281, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 291, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test2/my-supabase-blog/src/components/back-to-top.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const BackToTop = registerClientReference(\n    function() { throw new Error(\"Attempted to call BackToTop() from the server but BackToTop is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/back-to-top.tsx <module evaluation>\",\n    \"BackToTop\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,YAAY,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC3C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,gEACA", "debugId": null}}, {"offset": {"line": 305, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test2/my-supabase-blog/src/components/back-to-top.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const BackToTop = registerClientReference(\n    function() { throw new Error(\"Attempted to call BackToTop() from the server but BackToTop is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/back-to-top.tsx\",\n    \"BackToTop\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,YAAY,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC3C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,4CACA", "debugId": null}}, {"offset": {"line": 319, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 329, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test2/my-supabase-blog/src/components/post-date.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const PostDate = registerClientReference(\n    function() { throw new Error(\"Attempted to call PostDate() from the server but PostDate is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/post-date.tsx <module evaluation>\",\n    \"PostDate\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,WAAW,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC1C;IAAa,MAAM,IAAI,MAAM;AAAgO,GAC7P,8DACA", "debugId": null}}, {"offset": {"line": 343, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test2/my-supabase-blog/src/components/post-date.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const PostDate = registerClientReference(\n    function() { throw new Error(\"Attempted to call PostDate() from the server but PostDate is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/post-date.tsx\",\n    \"PostDate\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,WAAW,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC1C;IAAa,MAAM,IAAI,MAAM;AAAgO,GAC7P,0CACA", "debugId": null}}, {"offset": {"line": 357, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 367, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test2/my-supabase-blog/src/app/posts/%5Bid%5D/page.tsx"], "sourcesContent": ["import { notFound } from 'next/navigation'\nimport Link from 'next/link'\nimport { createClient } from '@/lib/supabase/server'\nimport { getCurrentUser, isAdmin } from '@/lib/auth'\nimport { Post } from '@/types/database'\nimport { MarkdownRenderer } from '@/components/markdown-renderer'\nimport { ClientThemeToggle } from '@/components/client-theme-toggle'\nimport { AdminActions } from '@/components/admin-actions'\nimport { BackToTop } from '@/components/back-to-top'\nimport { PostDate } from '@/components/post-date'\n\nasync function getPost(id: string): Promise<Post | null> {\n  const supabase = await createClient()\n  const { data: post, error } = await supabase\n    .from('posts')\n    .select('*')\n    .eq('id', id)\n    .single()\n\n  if (error) {\n    console.error('Error fetching post:', error)\n    return null\n  }\n\n  return post\n}\n\nexport default async function PostPage({ params }: { params: { id: string } }) {\n  const { id } = params\n  const post = await getPost(id)\n  \n  if (!post) {\n    notFound()\n  }\n\n  const user = await getCurrentUser()\n  const userIsAdmin = user ? await isAdmin(user.id) : false\n\n  return (\n    <div className=\"min-h-screen bg-background\">\n      {/* Header */}\n      <header className=\"bg-card/95 shadow-sm border-b border-border sticky top-0 z-50 backdrop-blur-sm\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4 lg:py-6\">\n          <div className=\"flex justify-between items-center\">\n            <Link\n              href=\"/\"\n              className=\"text-xl sm:text-2xl font-bold text-foreground hover:text-primary transition-colors\"\n            >\n              My Blog\n            </Link>\n            <div className=\"flex items-center gap-2 sm:gap-4\">\n              <ClientThemeToggle />\n              {user ? (\n                <>\n                  <span className=\"hidden sm:block text-sm text-muted-foreground\">\n                    Welcome, {user.email}\n                  </span>\n                  {userIsAdmin && (\n                    <>\n                      <Link\n                        href=\"/admin/new-post\"\n                        className=\"bg-primary text-primary-foreground px-3 sm:px-4 py-2 rounded-md hover:bg-primary/90 transition-all duration-200 text-sm font-medium shadow-sm hover:shadow-md\"\n                      >\n                        <span className=\"hidden sm:inline\">New Post</span>\n                        <span className=\"sm:hidden\">+</span>\n                      </Link>\n                      <Link\n                        href=\"/admin/manage-posts\"\n                        className=\"bg-secondary text-secondary-foreground px-3 sm:px-4 py-2 rounded-md hover:bg-secondary/80 transition-all duration-200 text-sm font-medium shadow-sm hover:shadow-md\"\n                      >\n                        <span className=\"hidden sm:inline\">Manage</span>\n                        <span className=\"sm:hidden\">⚙️</span>\n                      </Link>\n                    </>\n                  )}\n                  <form action=\"/auth/signout\" method=\"post\">\n                    <button\n                      type=\"submit\"\n                      className=\"inline-flex items-center justify-center px-3 py-2 rounded-md text-sm font-medium transition-colors bg-destructive text-destructive-foreground hover:bg-destructive/90 shadow-sm hover:shadow-md\"\n                    >\n                      <svg className=\"w-4 h-4 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1\" />\n                      </svg>\n                      Sign Out\n                    </button>\n                  </form>\n                </>\n              ) : (\n                <Link\n                  href=\"/login\"\n                  className=\"bg-primary text-primary-foreground px-3 sm:px-4 py-2 rounded-md hover:bg-primary/90 transition-all duration-200 text-sm font-medium shadow-sm hover:shadow-md\"\n                >\n                  Sign In\n                </Link>\n              )}\n            </div>\n          </div>\n        </div>\n      </header>\n\n      {/* Main Content */}\n      <main className=\"max-w-4xl mx-auto px-3 sm:px-4 lg:px-6 py-4 sm:py-6 lg:py-8\">\n        {/* Navigation Breadcrumb */}\n        <nav className=\"mb-4 sm:mb-6 lg:mb-8\" aria-label=\"Breadcrumb\">\n          <div className=\"flex items-center space-x-2 text-sm\">\n            <Link\n              href=\"/\"\n              className=\"inline-flex items-center text-muted-foreground hover:text-primary transition-all duration-200 group font-medium\"\n            >\n              <svg className=\"w-4 h-4 mr-2 group-hover:-translate-x-1 transition-transform\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6\" />\n              </svg>\n              Home\n            </Link>\n            <svg className=\"w-4 h-4 text-muted-foreground\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 5l7 7-7 7\" />\n            </svg>\n            <span className=\"text-foreground font-medium truncate max-w-xs sm:max-w-md\">\n              {post.title}\n            </span>\n          </div>\n        </nav>\n\n        {/* Article Content */}\n        <article className=\"bg-card rounded-2xl shadow-xl border border-border overflow-hidden animate-fade-in\">\n          {/* Article Header */}\n          <header className=\"relative p-4 sm:p-6 lg:p-8 border-b border-border\">\n            {/* Background Pattern */}\n            <div className=\"absolute inset-0 bg-gradient-to-br from-primary/5 via-transparent to-secondary/5\"></div>\n            <div className=\"absolute inset-0 bg-[radial-gradient(circle_at_30%_20%,rgba(120,119,198,0.1),transparent_50%)]\"></div>\n\n            <div className=\"relative\">\n              <h1 className=\"text-2xl sm:text-3xl lg:text-4xl font-bold text-card-foreground mb-4 sm:mb-6 leading-tight tracking-tight\">\n                {post.title}\n              </h1>\n\n              {/* Article Meta */}\n              <div className=\"flex flex-col sm:flex-row sm:items-center gap-3 sm:gap-4 text-sm\">\n                <div className=\"flex items-center gap-3 sm:gap-4\">\n                  <PostDate date={post.created_at} />\n\n                  {post.updated_at !== post.created_at && (\n                    <PostDate date={post.updated_at} isUpdateDate={true} />\n                  )}\n                </div>\n\n                {/* Reading Time Estimate */}\n                <div className=\"flex items-center text-muted-foreground bg-muted/50 px-2 sm:px-3 py-1 sm:py-2 rounded-full text-xs sm:text-sm\">\n                  <svg className=\"w-3 h-3 sm:w-4 sm:h-4 mr-1 sm:mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\" />\n                  </svg>\n                  {Math.max(1, Math.ceil(post.content.split(' ').length / 200))} min read\n                </div>\n              </div>\n            </div>\n          </header>\n\n          {/* Article Body */}\n          <div className=\"p-4 sm:p-6 lg:p-8\">\n            <div className=\"prose prose-base sm:prose-lg max-w-none dark:prose-invert\n                          prose-headings:text-foreground prose-headings:font-bold prose-headings:tracking-tight\n                          prose-p:text-muted-foreground prose-p:leading-relaxed prose-p:text-base sm:prose-p:text-lg\n                          prose-strong:text-foreground prose-strong:font-semibold\n                          prose-code:text-primary prose-code:bg-muted prose-code:px-2 prose-code:py-1 prose-code:rounded prose-code:text-sm\n                          prose-pre:bg-muted prose-pre:border prose-pre:border-border prose-pre:rounded-lg prose-pre:p-4\n                          prose-blockquote:border-l-primary prose-blockquote:bg-muted/30 prose-blockquote:pl-6 prose-blockquote:py-2\n                          prose-a:text-primary prose-a:no-underline hover:prose-a:underline prose-a:font-medium\n                          prose-ul:text-muted-foreground prose-ol:text-muted-foreground\n                          prose-li:text-muted-foreground prose-li:leading-relaxed\n                          prose-img:rounded-lg prose-img:shadow-md prose-img:border prose-img:border-border\n                          prose-hr:border-border prose-hr:my-8\">\n              <MarkdownRenderer content={post.content} />\n            </div>\n          </div>\n        </article>\n\n        {/* Admin Actions */}\n        {userIsAdmin && (\n          <div className=\"mt-6 sm:mt-8 p-4 sm:p-6 bg-gradient-to-r from-muted/50 to-muted/30 rounded-2xl border border-border shadow-lg animate-slide-in\">\n            <div className=\"flex items-center justify-between mb-6\">\n              <h3 className=\"text-xl font-bold text-foreground flex items-center\">\n                <div className=\"w-10 h-10 bg-primary/10 rounded-xl flex items-center justify-center mr-3\">\n                  <svg className=\"w-5 h-5 text-primary\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z\" />\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M15 12a3 3 0 11-6 0 3 3 0 016 0z\" />\n                  </svg>\n                </div>\n                Admin Actions\n              </h3>\n              <span className=\"text-xs text-muted-foreground bg-muted px-3 py-1 rounded-full\">\n                Admin Only\n              </span>\n            </div>\n            <AdminActions postId={post.id} />\n          </div>\n        )}\n\n      </main>\n\n      {/* Floating Back to Top Button */}\n      <BackToTop />\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAAA;AAAA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;;;;;;;;;;;AAEA,eAAe,QAAQ,EAAU;IAC/B,MAAM,WAAW,MAAM,CAAA,GAAA,gIAAA,CAAA,eAAY,AAAD;IAClC,MAAM,EAAE,MAAM,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SACjC,IAAI,CAAC,SACL,MAAM,CAAC,KACP,EAAE,CAAC,MAAM,IACT,MAAM;IAET,IAAI,OAAO;QACT,QAAQ,KAAK,CAAC,wBAAwB;QACtC,OAAO;IACT;IAEA,OAAO;AACT;AAEe,eAAe,SAAS,EAAE,MAAM,EAA8B;IAC3E,MAAM,EAAE,EAAE,EAAE,GAAG;IACf,MAAM,OAAO,MAAM,QAAQ;IAE3B,IAAI,CAAC,MAAM;QACT,CAAA,GAAA,qLAAA,CAAA,WAAQ,AAAD;IACT;IAEA,MAAM,OAAO,MAAM,CAAA,GAAA,kHAAA,CAAA,iBAAc,AAAD;IAChC,MAAM,cAAc,OAAO,MAAM,CAAA,GAAA,kHAAA,CAAA,UAAO,AAAD,EAAE,KAAK,EAAE,IAAI;IAEpD,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;0CACX;;;;;;0CAGD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,+IAAA,CAAA,oBAAiB;;;;;oCACjB,qBACC;;0DACE,8OAAC;gDAAK,WAAU;;oDAAgD;oDACpD,KAAK,KAAK;;;;;;;4CAErB,6BACC;;kEACE,8OAAC,4JAAA,CAAA,UAAI;wDACH,MAAK;wDACL,WAAU;;0EAEV,8OAAC;gEAAK,WAAU;0EAAmB;;;;;;0EACnC,8OAAC;gEAAK,WAAU;0EAAY;;;;;;;;;;;;kEAE9B,8OAAC,4JAAA,CAAA,UAAI;wDACH,MAAK;wDACL,WAAU;;0EAEV,8OAAC;gEAAK,WAAU;0EAAmB;;;;;;0EACnC,8OAAC;gEAAK,WAAU;0EAAY;;;;;;;;;;;;;;0DAIlC,8OAAC;gDAAK,QAAO;gDAAgB,QAAO;0DAClC,cAAA,8OAAC;oDACC,MAAK;oDACL,WAAU;;sEAEV,8OAAC;4DAAI,WAAU;4DAAe,MAAK;4DAAO,QAAO;4DAAe,SAAQ;sEACtE,cAAA,8OAAC;gEAAK,eAAc;gEAAQ,gBAAe;gEAAQ,aAAa;gEAAG,GAAE;;;;;;;;;;;wDACjE;;;;;;;;;;;;;qEAMZ,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAUX,8OAAC;gBAAK,WAAU;;kCAEd,8OAAC;wBAAI,WAAU;wBAAuB,cAAW;kCAC/C,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;;sDAEV,8OAAC;4CAAI,WAAU;4CAA+D,MAAK;4CAAO,QAAO;4CAAe,SAAQ;sDACtH,cAAA,8OAAC;gDAAK,eAAc;gDAAQ,gBAAe;gDAAQ,aAAa;gDAAG,GAAE;;;;;;;;;;;wCACjE;;;;;;;8CAGR,8OAAC;oCAAI,WAAU;oCAAgC,MAAK;oCAAO,QAAO;oCAAe,SAAQ;8CACvF,cAAA,8OAAC;wCAAK,eAAc;wCAAQ,gBAAe;wCAAQ,aAAa;wCAAG,GAAE;;;;;;;;;;;8CAEvE,8OAAC;oCAAK,WAAU;8CACb,KAAK,KAAK;;;;;;;;;;;;;;;;;kCAMjB,8OAAC;wBAAQ,WAAU;;0CAEjB,8OAAC;gCAAO,WAAU;;kDAEhB,8OAAC;wCAAI,WAAU;;;;;;kDACf,8OAAC;wCAAI,WAAU;;;;;;kDAEf,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DACX,KAAK,KAAK;;;;;;0DAIb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,kIAAA,CAAA,WAAQ;gEAAC,MAAM,KAAK,UAAU;;;;;;4DAE9B,KAAK,UAAU,KAAK,KAAK,UAAU,kBAClC,8OAAC,kIAAA,CAAA,WAAQ;gEAAC,MAAM,KAAK,UAAU;gEAAE,cAAc;;;;;;;;;;;;kEAKnD,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;gEAAqC,MAAK;gEAAO,QAAO;gEAAe,SAAQ;0EAC5F,cAAA,8OAAC;oEAAK,eAAc;oEAAQ,gBAAe;oEAAQ,aAAa;oEAAG,GAAE;;;;;;;;;;;4DAEtE,KAAK,GAAG,CAAC,GAAG,KAAK,IAAI,CAAC,KAAK,OAAO,CAAC,KAAK,CAAC,KAAK,MAAM,GAAG;4DAAM;;;;;;;;;;;;;;;;;;;;;;;;;0CAOtE,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;8CAYb,cAAA,8OAAC,0IAAA,CAAA,mBAAgB;wCAAC,SAAS,KAAK,OAAO;;;;;;;;;;;;;;;;;;;;;;oBAM5C,6BACC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;;0DACZ,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;oDAAuB,MAAK;oDAAO,QAAO;oDAAe,SAAQ;;sEAC9E,8OAAC;4DAAK,eAAc;4DAAQ,gBAAe;4DAAQ,aAAa;4DAAG,GAAE;;;;;;sEACrE,8OAAC;4DAAK,eAAc;4DAAQ,gBAAe;4DAAQ,aAAa;4DAAG,GAAE;;;;;;;;;;;;;;;;;4CAEnE;;;;;;;kDAGR,8OAAC;wCAAK,WAAU;kDAAgE;;;;;;;;;;;;0CAIlF,8OAAC,sIAAA,CAAA,eAAY;gCAAC,QAAQ,KAAK,EAAE;;;;;;;;;;;;;;;;;;0BAOnC,8OAAC,uIAAA,CAAA,YAAS;;;;;;;;;;;AAGhB", "debugId": null}}]}