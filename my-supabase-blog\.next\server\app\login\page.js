(()=>{var e={};e.id=520,e.ids=[520],e.modules={440:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>n});var s=t(1658);let n=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,s.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1135:()=>{},1339:(e,r,t)=>{"use strict";t.d(r,{ThemeToggle:()=>d});var s=t(687),n=t(3210),i=t(2688);let o=(0,i.A)("sun",[["circle",{cx:"12",cy:"12",r:"4",key:"4exip2"}],["path",{d:"M12 2v2",key:"tus03m"}],["path",{d:"M12 20v2",key:"1lh1kg"}],["path",{d:"m4.93 4.93 1.41 1.41",key:"149t6j"}],["path",{d:"m17.66 17.66 1.41 1.41",key:"ptbguv"}],["path",{d:"M2 12h2",key:"1t8f8n"}],["path",{d:"M20 12h2",key:"1q8mjw"}],["path",{d:"m6.34 17.66-1.41 1.41",key:"1m8zz5"}],["path",{d:"m19.07 4.93-1.41 1.41",key:"1shlcs"}]]),a=(0,i.A)("moon",[["path",{d:"M12 3a6 6 0 0 0 9 9 9 9 0 1 1-9-9Z",key:"a7tn18"}]]);var l=t(218);function d(){let{theme:e,setTheme:r}=(0,l.D)(),[t,i]=n.useState(!1);return(n.useEffect(()=>{i(!0)},[]),t)?(0,s.jsxs)("button",{onClick:()=>r("light"===e?"dark":"light"),className:"inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none ring-offset-background border border-input bg-background hover:bg-accent hover:text-accent-foreground h-10 w-10",children:["light"===e?(0,s.jsx)(a,{className:"h-[1.2rem] w-[1.2rem]"}):(0,s.jsx)(o,{className:"h-[1.2rem] w-[1.2rem]"}),(0,s.jsx)("span",{className:"sr-only",children:"切換主題"})]}):(0,s.jsxs)("button",{className:"inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none ring-offset-background border border-input bg-background hover:bg-accent hover:text-accent-foreground h-10 w-10",children:[(0,s.jsx)(o,{className:"h-[1.2rem] w-[1.2rem]"}),(0,s.jsx)("span",{className:"sr-only",children:"切換主題"})]})}},1630:e=>{"use strict";e.exports=require("http")},1645:e=>{"use strict";e.exports=require("net")},1874:(e,r,t)=>{Promise.resolve().then(t.bind(t,4934))},1997:e=>{"use strict";e.exports=require("punycode")},2083:(e,r,t)=>{Promise.resolve().then(t.bind(t,3701))},2331:(e,r,t)=>{Promise.resolve().then(t.bind(t,6871))},2500:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>o.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>m,tree:()=>d});var s=t(5239),n=t(8088),i=t(8170),o=t.n(i),a=t(893),l={};for(let e in a)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>a[e]);t.d(r,l);let d={children:["",{children:["login",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,4934)),"C:\\Users\\<USER>\\Desktop\\test2\\my-supabase-blog\\src\\app\\login\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,4431)),"C:\\Users\\<USER>\\Desktop\\test2\\my-supabase-blog\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,5284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\Desktop\\test2\\my-supabase-blog\\src\\app\\login\\page.tsx"],u={require:t,loadChunk:()=>Promise.resolve()},m=new s.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/login/page",pathname:"/login",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},2688:(e,r,t)=>{"use strict";t.d(r,{A:()=>u});var s=t(3210);let n=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),i=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,r,t)=>t?t.toUpperCase():r.toLowerCase()),o=e=>{let r=i(e);return r.charAt(0).toUpperCase()+r.slice(1)},a=(...e)=>e.filter((e,r,t)=>!!e&&""!==e.trim()&&t.indexOf(e)===r).join(" ").trim(),l=e=>{for(let r in e)if(r.startsWith("aria-")||"role"===r||"title"===r)return!0};var d={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let c=(0,s.forwardRef)(({color:e="currentColor",size:r=24,strokeWidth:t=2,absoluteStrokeWidth:n,className:i="",children:o,iconNode:c,...u},m)=>(0,s.createElement)("svg",{ref:m,...d,width:r,height:r,stroke:e,strokeWidth:n?24*Number(t)/Number(r):t,className:a("lucide",i),...!o&&!l(u)&&{"aria-hidden":"true"},...u},[...c.map(([e,r])=>(0,s.createElement)(e,r)),...Array.isArray(o)?o:[o]])),u=(e,r)=>{let t=(0,s.forwardRef)(({className:t,...i},l)=>(0,s.createElement)(c,{ref:l,iconNode:r,className:a(`lucide-${n(o(e))}`,`lucide-${e}`,t),...i}));return t.displayName=o(e),t}},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3701:(e,r,t)=>{"use strict";t.d(r,{ThemeProvider:()=>s});let s=(0,t(2907).registerClientReference)(function(){throw Error("Attempted to call ThemeProvider() from the server but ThemeProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\test2\\my-supabase-blog\\src\\components\\theme-provider.tsx","ThemeProvider")},3873:e=>{"use strict";e.exports=require("path")},4075:e=>{"use strict";e.exports=require("zlib")},4431:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>c,metadata:()=>d});var s=t(7413),n=t(2376),i=t.n(n),o=t(8726),a=t.n(o);t(1135);var l=t(3701);let d={title:"我的部落格",description:"使用 Next.js 和 Supabase 建立的現代化部落格"};function c({children:e}){return(0,s.jsx)("html",{lang:"zh-TW",suppressHydrationWarning:!0,children:(0,s.jsx)("body",{className:`${i().variable} ${a().variable} antialiased`,suppressHydrationWarning:!0,children:(0,s.jsx)(l.ThemeProvider,{attribute:"class",defaultTheme:"system",enableSystem:!0,disableTransitionOnChange:!0,children:e})})})}},4631:e=>{"use strict";e.exports=require("tls")},4735:e=>{"use strict";e.exports=require("events")},4934:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>s});let s=(0,t(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test2\\\\my-supabase-blog\\\\src\\\\app\\\\login\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\test2\\my-supabase-blog\\src\\app\\login\\page.tsx","default")},5247:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,6444,23)),Promise.resolve().then(t.t.bind(t,6042,23)),Promise.resolve().then(t.t.bind(t,8170,23)),Promise.resolve().then(t.t.bind(t,9477,23)),Promise.resolve().then(t.t.bind(t,9345,23)),Promise.resolve().then(t.t.bind(t,2089,23)),Promise.resolve().then(t.t.bind(t,6577,23)),Promise.resolve().then(t.t.bind(t,1307,23))},5511:e=>{"use strict";e.exports=require("crypto")},5591:e=>{"use strict";e.exports=require("https")},6378:(e,r,t)=>{Promise.resolve().then(t.bind(t,9488))},6871:(e,r,t)=>{"use strict";t.d(r,{ThemeProvider:()=>i});var s=t(687);t(3210);var n=t(218);function i({children:e,...r}){return(0,s.jsx)(n.N,{...r,children:e})}},7910:e=>{"use strict";e.exports=require("stream")},7990:()=>{},8375:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,6346,23)),Promise.resolve().then(t.t.bind(t,7924,23)),Promise.resolve().then(t.t.bind(t,5656,23)),Promise.resolve().then(t.t.bind(t,99,23)),Promise.resolve().then(t.t.bind(t,8243,23)),Promise.resolve().then(t.t.bind(t,8827,23)),Promise.resolve().then(t.t.bind(t,2763,23)),Promise.resolve().then(t.t.bind(t,7173,23))},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9428:e=>{"use strict";e.exports=require("buffer")},9481:(e,r,t)=>{"use strict";t.d(r,{U:()=>n});var s=t(9522);function n(){return(0,s.createBrowserClient)("https://sqoixvgmroejgaebxyeq.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InNxb2l4dmdtcm9lamdhZWJ4eWVxIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTExNjU0ODksImV4cCI6MjA2Njc0MTQ4OX0.SFkF9v0Y-lrJuQHiSf5HLTLuwgOXRf0ERQKnfQNfLsU")}},9488:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>c});var s=t(687),n=t(3210),i=t(9481),o=t(6189),a=t(5814),l=t.n(a),d=t(1339);function c(){let[e,r]=(0,n.useState)(""),[t,a]=(0,n.useState)(""),[c,u]=(0,n.useState)(!1),[m,p]=(0,n.useState)(""),[h,b]=(0,n.useState)(!1),x=(0,o.useRouter)(),f=(0,i.U)(),g=async r=>{r.preventDefault(),u(!0),p("");try{if(h){let{error:r}=await f.auth.signUp({email:e,password:t});if(r)throw r;p("註冊成功！請檢查您的電子郵件以驗證帳戶。")}else{let{error:r}=await f.auth.signInWithPassword({email:e,password:t});if(r)throw r;x.push("/"),x.refresh()}}catch(e){p(e instanceof Error?e.message:"發生錯誤")}finally{u(!1)}};return(0,s.jsxs)("div",{className:"min-h-screen bg-background",children:[(0,s.jsx)("header",{className:"bg-card shadow-sm border-b border-border",children:(0,s.jsx)("div",{className:"max-w-4xl mx-auto px-4 py-6",children:(0,s.jsxs)("div",{className:"flex justify-between items-center",children:[(0,s.jsx)(l(),{href:"/",className:"text-2xl font-bold text-foreground hover:text-primary transition-colors",children:"我的部落格"}),(0,s.jsx)(d.ThemeToggle,{})]})})}),(0,s.jsx)("div",{className:"flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8",children:(0,s.jsxs)("div",{className:"max-w-md w-full space-y-8",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h2",{className:"mt-6 text-center text-3xl font-extrabold text-foreground",children:h?"建立新帳戶":"登入您的帳戶"}),(0,s.jsxs)("p",{className:"mt-2 text-center text-sm text-muted-foreground",children:["或"," ",(0,s.jsx)(l(),{href:"/",className:"font-medium text-primary hover:text-primary/80",children:"返回首頁"})]})]}),(0,s.jsx)("div",{className:"bg-card rounded-lg shadow-sm border border-border p-8",children:(0,s.jsxs)("form",{className:"space-y-6",onSubmit:g,children:[(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"email",className:"block text-sm font-medium text-foreground mb-2",children:"電子郵件"}),(0,s.jsx)("input",{id:"email",name:"email",type:"email",autoComplete:"email",required:!0,className:"w-full px-3 py-2 border border-input bg-background text-foreground rounded-md focus:outline-none focus:ring-2 focus:ring-ring focus:border-transparent",placeholder:"輸入您的電子郵件地址",value:e,onChange:e=>r(e.target.value)})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"password",className:"block text-sm font-medium text-foreground mb-2",children:"密碼"}),(0,s.jsx)("input",{id:"password",name:"password",type:"password",autoComplete:"current-password",required:!0,className:"w-full px-3 py-2 border border-input bg-background text-foreground rounded-md focus:outline-none focus:ring-2 focus:ring-ring focus:border-transparent",placeholder:"輸入您的密碼",value:t,onChange:e=>a(e.target.value)})]})]}),m&&(0,s.jsx)("div",{className:`text-sm text-center p-3 rounded-md ${m.includes("成功")?"bg-green-50 text-green-700 border border-green-200 dark:bg-green-900/20 dark:text-green-400 dark:border-green-800":"bg-destructive/10 text-destructive border border-destructive/20"}`,children:m}),(0,s.jsx)("div",{children:(0,s.jsx)("button",{type:"submit",disabled:c,className:"w-full bg-primary text-primary-foreground py-2 px-4 rounded-md hover:bg-primary/90 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors",children:c?"處理中...":h?"註冊":"登入"})}),(0,s.jsx)("div",{className:"text-center",children:(0,s.jsx)("button",{type:"button",onClick:()=>b(!h),className:"text-primary hover:text-primary/80 text-sm transition-colors",children:h?"已有帳戶？點此登入":"沒有帳戶？點此註冊"})})]})})]})})]})}},9551:e=>{"use strict";e.exports=require("url")},9727:()=>{}};var r=require("../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[447,98,567,318],()=>t(2500));module.exports=s})();