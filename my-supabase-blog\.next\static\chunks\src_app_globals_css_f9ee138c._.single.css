/* [project]/src/app/globals.css [app-client] (css) */
@layer properties {
  @supports (((-webkit-hyphens: none)) and (not (margin-trim: inline))) or ((-moz-orient: inline) and (not (color: rgb(from red r g b)))) {
    *, :before, :after, ::backdrop {
      --tw-translate-x: 0;
      --tw-translate-y: 0;
      --tw-translate-z: 0;
      --tw-space-y-reverse: 0;
      --tw-space-x-reverse: 0;
      --tw-divide-y-reverse: 0;
      --tw-border-style: solid;
      --tw-gradient-position: initial;
      --tw-gradient-from: #0000;
      --tw-gradient-via: #0000;
      --tw-gradient-to: #0000;
      --tw-gradient-stops: initial;
      --tw-gradient-via-stops: initial;
      --tw-gradient-from-position: 0%;
      --tw-gradient-via-position: 50%;
      --tw-gradient-to-position: 100%;
      --tw-leading: initial;
      --tw-font-weight: initial;
      --tw-tracking: initial;
      --tw-shadow: 0 0 #0000;
      --tw-shadow-color: initial;
      --tw-shadow-alpha: 100%;
      --tw-inset-shadow: 0 0 #0000;
      --tw-inset-shadow-color: initial;
      --tw-inset-shadow-alpha: 100%;
      --tw-ring-color: initial;
      --tw-ring-shadow: 0 0 #0000;
      --tw-inset-ring-color: initial;
      --tw-inset-ring-shadow: 0 0 #0000;
      --tw-ring-inset: initial;
      --tw-ring-offset-width: 0px;
      --tw-ring-offset-color: #fff;
      --tw-ring-offset-shadow: 0 0 #0000;
      --tw-backdrop-blur: initial;
      --tw-backdrop-brightness: initial;
      --tw-backdrop-contrast: initial;
      --tw-backdrop-grayscale: initial;
      --tw-backdrop-hue-rotate: initial;
      --tw-backdrop-invert: initial;
      --tw-backdrop-opacity: initial;
      --tw-backdrop-saturate: initial;
      --tw-backdrop-sepia: initial;
      --tw-duration: initial;
      --tw-scale-x: 1;
      --tw-scale-y: 1;
      --tw-scale-z: 1;
    }
  }
}

@layer theme {
  :root, :host {
    --font-sans: var(--font-geist-sans);
    --color-red-50: oklch(97.1% .013 17.38);
    --color-red-300: oklch(80.8% .114 19.571);
    --color-red-400: oklch(70.4% .191 22.216);
    --color-red-600: oklch(57.7% .245 27.325);
    --color-red-700: oklch(50.5% .213 27.518);
    --color-red-900: oklch(39.6% .141 25.723);
    --color-green-50: oklch(98.2% .018 155.826);
    --color-green-100: oklch(96.2% .044 156.743);
    --color-green-200: oklch(92.5% .084 155.995);
    --color-green-300: oklch(87.1% .15 154.449);
    --color-green-400: oklch(79.2% .209 151.711);
    --color-green-600: oklch(62.7% .194 149.214);
    --color-green-700: oklch(52.7% .154 150.069);
    --color-green-800: oklch(44.8% .119 151.328);
    --color-green-900: oklch(39.3% .095 152.535);
    --color-blue-50: oklch(97% .014 254.604);
    --color-blue-100: oklch(93.2% .032 255.585);
    --color-blue-300: oklch(80.9% .105 251.813);
    --color-blue-400: oklch(70.7% .165 254.624);
    --color-blue-600: oklch(54.6% .245 262.881);
    --color-blue-700: oklch(48.8% .243 264.376);
    --color-blue-900: oklch(37.9% .146 265.522);
    --spacing: .25rem;
    --container-xs: 20rem;
    --container-md: 28rem;
    --container-4xl: 56rem;
    --container-7xl: 80rem;
    --text-xs: .75rem;
    --text-xs--line-height: calc(1 / .75);
    --text-sm: .875rem;
    --text-sm--line-height: calc(1.25 / .875);
    --text-base: 1rem;
    --text-base--line-height: calc(1.5 / 1);
    --text-lg: 1.125rem;
    --text-lg--line-height: calc(1.75 / 1.125);
    --text-xl: 1.25rem;
    --text-xl--line-height: calc(1.75 / 1.25);
    --text-2xl: 1.5rem;
    --text-2xl--line-height: calc(2 / 1.5);
    --text-3xl: 1.875rem;
    --text-3xl--line-height: calc(2.25 / 1.875);
    --text-4xl: 2.25rem;
    --text-4xl--line-height: calc(2.5 / 2.25);
    --font-weight-medium: 500;
    --font-weight-semibold: 600;
    --font-weight-bold: 700;
    --tracking-tight: -.025em;
    --tracking-wider: .05em;
    --leading-tight: 1.25;
    --leading-relaxed: 1.625;
    --radius-md: .375rem;
    --radius-lg: .5rem;
    --radius-xl: .75rem;
    --radius-2xl: 1rem;
    --animate-spin: spin 1s linear infinite;
    --animate-pulse: pulse 2s cubic-bezier(.4, 0, .6, 1) infinite;
    --blur-sm: 8px;
    --default-transition-duration: .15s;
    --default-transition-timing-function: cubic-bezier(.4, 0, .2, 1);
    --default-font-family: var(--font-geist-sans);
    --default-mono-font-family: var(--font-geist-mono);
    --radius: var(--radius);
  }
}

@layer base {
  *, :after, :before, ::backdrop, ::file-selector-button {
    box-sizing: border-box;
    border: 0 solid;
    margin: 0;
    padding: 0;
  }

  html, :host {
    -webkit-text-size-adjust: 100%;
    tab-size: 4;
    line-height: 1.5;
    font-family: var(--default-font-family, ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji");
    font-feature-settings: var(--default-font-feature-settings, normal);
    font-variation-settings: var(--default-font-variation-settings, normal);
    -webkit-tap-highlight-color: transparent;
  }

  hr {
    height: 0;
    color: inherit;
    border-top-width: 1px;
  }

  abbr:where([title]) {
    text-decoration: underline dotted;
  }

  h1, h2, h3, h4, h5, h6 {
    font-size: inherit;
    font-weight: inherit;
  }

  a {
    color: inherit;
    -webkit-text-decoration: inherit;
    text-decoration: inherit;
  }

  b, strong {
    font-weight: bolder;
  }

  code, kbd, samp, pre {
    font-family: var(--default-mono-font-family, ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace);
    font-feature-settings: var(--default-mono-font-feature-settings, normal);
    font-variation-settings: var(--default-mono-font-variation-settings, normal);
    font-size: 1em;
  }

  small {
    font-size: 80%;
  }

  sub, sup {
    vertical-align: baseline;
    font-size: 75%;
    line-height: 0;
    position: relative;
  }

  sub {
    bottom: -.25em;
  }

  sup {
    top: -.5em;
  }

  table {
    text-indent: 0;
    border-color: inherit;
    border-collapse: collapse;
  }

  :-moz-focusring {
    outline: auto;
  }

  progress {
    vertical-align: baseline;
  }

  summary {
    display: list-item;
  }

  ol, ul, menu {
    list-style: none;
  }

  img, svg, video, canvas, audio, iframe, embed, object {
    vertical-align: middle;
    display: block;
  }

  img, video {
    max-width: 100%;
    height: auto;
  }

  button, input, select, optgroup, textarea, ::file-selector-button {
    font: inherit;
    font-feature-settings: inherit;
    font-variation-settings: inherit;
    letter-spacing: inherit;
    color: inherit;
    opacity: 1;
    background-color: #0000;
    border-radius: 0;
  }

  :where(select:is([multiple], [size])) optgroup {
    font-weight: bolder;
  }

  :where(select:is([multiple], [size])) optgroup option {
    padding-inline-start: 20px;
  }

  ::file-selector-button {
    margin-inline-end: 4px;
  }

  ::placeholder {
    opacity: 1;
  }

  @supports (not ((-webkit-appearance: -apple-pay-button))) or (contain-intrinsic-size: 1px) {
    ::placeholder {
      color: currentColor;
    }

    @supports (color: color-mix(in lab, red, red)) {
      ::placeholder {
        color: color-mix(in oklab, currentcolor 50%, transparent);
      }
    }
  }

  textarea {
    resize: vertical;
  }

  ::-webkit-search-decoration {
    -webkit-appearance: none;
  }

  ::-webkit-date-and-time-value {
    min-height: 1lh;
    text-align: inherit;
  }

  ::-webkit-datetime-edit {
    display: inline-flex;
  }

  ::-webkit-datetime-edit-fields-wrapper {
    padding: 0;
  }

  ::-webkit-datetime-edit, ::-webkit-datetime-edit-year-field, ::-webkit-datetime-edit-month-field, ::-webkit-datetime-edit-day-field, ::-webkit-datetime-edit-hour-field, ::-webkit-datetime-edit-minute-field, ::-webkit-datetime-edit-second-field, ::-webkit-datetime-edit-millisecond-field, ::-webkit-datetime-edit-meridiem-field {
    padding-block: 0;
  }

  :-moz-ui-invalid {
    box-shadow: none;
  }

  button, input:where([type="button"], [type="reset"], [type="submit"]), ::file-selector-button {
    appearance: button;
  }

  ::-webkit-inner-spin-button, ::-webkit-outer-spin-button {
    height: auto;
  }

  [hidden]:where(:not([hidden="until-found"])) {
    display: none !important;
  }
}

@layer components;

@layer utilities {
  .sr-only {
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border-width: 0;
    width: 1px;
    height: 1px;
    margin: -1px;
    padding: 0;
    position: absolute;
    overflow: hidden;
  }

  .absolute {
    position: absolute;
  }

  .fixed {
    position: fixed;
  }

  .relative {
    position: relative;
  }

  .sticky {
    position: sticky;
  }

  .inset-0 {
    inset: calc(var(--spacing) * 0);
  }

  .top-0 {
    top: calc(var(--spacing) * 0);
  }

  .top-1\/2 {
    top: 50%;
  }

  .right-3 {
    right: calc(var(--spacing) * 3);
  }

  .right-8 {
    right: calc(var(--spacing) * 8);
  }

  .bottom-8 {
    bottom: calc(var(--spacing) * 8);
  }

  .z-50 {
    z-index: 50;
  }

  .mx-auto {
    margin-inline: auto;
  }

  .mt-2 {
    margin-top: calc(var(--spacing) * 2);
  }

  .mt-3 {
    margin-top: calc(var(--spacing) * 3);
  }

  .mt-4 {
    margin-top: calc(var(--spacing) * 4);
  }

  .mt-6 {
    margin-top: calc(var(--spacing) * 6);
  }

  .mt-8 {
    margin-top: calc(var(--spacing) * 8);
  }

  .mr-1 {
    margin-right: calc(var(--spacing) * 1);
  }

  .mr-2 {
    margin-right: calc(var(--spacing) * 2);
  }

  .mr-3 {
    margin-right: calc(var(--spacing) * 3);
  }

  .mr-4 {
    margin-right: calc(var(--spacing) * 4);
  }

  .mb-1 {
    margin-bottom: calc(var(--spacing) * 1);
  }

  .mb-2 {
    margin-bottom: calc(var(--spacing) * 2);
  }

  .mb-3 {
    margin-bottom: calc(var(--spacing) * 3);
  }

  .mb-4 {
    margin-bottom: calc(var(--spacing) * 4);
  }

  .mb-6 {
    margin-bottom: calc(var(--spacing) * 6);
  }

  .mb-8 {
    margin-bottom: calc(var(--spacing) * 8);
  }

  .ml-1 {
    margin-left: calc(var(--spacing) * 1);
  }

  .ml-4 {
    margin-left: calc(var(--spacing) * 4);
  }

  .line-clamp-2 {
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    display: -webkit-box;
    overflow: hidden;
  }

  .line-clamp-3 {
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    display: -webkit-box;
    overflow: hidden;
  }

  .block {
    display: block;
  }

  .flex {
    display: flex;
  }

  .grid {
    display: grid;
  }

  .hidden {
    display: none;
  }

  .inline-flex {
    display: inline-flex;
  }

  .h-1 {
    height: calc(var(--spacing) * 1);
  }

  .h-2 {
    height: calc(var(--spacing) * 2);
  }

  .h-3 {
    height: calc(var(--spacing) * 3);
  }

  .h-4 {
    height: calc(var(--spacing) * 4);
  }

  .h-5 {
    height: calc(var(--spacing) * 5);
  }

  .h-6 {
    height: calc(var(--spacing) * 6);
  }

  .h-8 {
    height: calc(var(--spacing) * 8);
  }

  .h-9 {
    height: calc(var(--spacing) * 9);
  }

  .h-10 {
    height: calc(var(--spacing) * 10);
  }

  .h-12 {
    height: calc(var(--spacing) * 12);
  }

  .h-16 {
    height: calc(var(--spacing) * 16);
  }

  .h-\[1\.2rem\] {
    height: 1.2rem;
  }

  .min-h-screen {
    min-height: 100vh;
  }

  .w-2 {
    width: calc(var(--spacing) * 2);
  }

  .w-2\/3 {
    width: 66.6667%;
  }

  .w-3 {
    width: calc(var(--spacing) * 3);
  }

  .w-3\/4 {
    width: 75%;
  }

  .w-4 {
    width: calc(var(--spacing) * 4);
  }

  .w-5 {
    width: calc(var(--spacing) * 5);
  }

  .w-6 {
    width: calc(var(--spacing) * 6);
  }

  .w-8 {
    width: calc(var(--spacing) * 8);
  }

  .w-9 {
    width: calc(var(--spacing) * 9);
  }

  .w-10 {
    width: calc(var(--spacing) * 10);
  }

  .w-12 {
    width: calc(var(--spacing) * 12);
  }

  .w-16 {
    width: calc(var(--spacing) * 16);
  }

  .w-\[1\.2rem\] {
    width: 1.2rem;
  }

  .w-full {
    width: 100%;
  }

  .max-w-4xl {
    max-width: var(--container-4xl);
  }

  .max-w-7xl {
    max-width: var(--container-7xl);
  }

  .max-w-md {
    max-width: var(--container-md);
  }

  .max-w-none {
    max-width: none;
  }

  .max-w-xs {
    max-width: var(--container-xs);
  }

  .min-w-0 {
    min-width: calc(var(--spacing) * 0);
  }

  .flex-1 {
    flex: 1;
  }

  .flex-shrink-0 {
    flex-shrink: 0;
  }

  .-translate-y-1\/2 {
    --tw-translate-y: calc(calc(1 / 2 * 100%) * -1);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .animate-pulse {
    animation: var(--animate-pulse);
  }

  .animate-spin {
    animation: var(--animate-spin);
  }

  .resize {
    resize: both;
  }

  .grid-cols-1 {
    grid-template-columns: repeat(1, minmax(0, 1fr));
  }

  .flex-col {
    flex-direction: column;
  }

  .items-center {
    align-items: center;
  }

  .items-start {
    align-items: flex-start;
  }

  .justify-between {
    justify-content: space-between;
  }

  .justify-center {
    justify-content: center;
  }

  .gap-2 {
    gap: calc(var(--spacing) * 2);
  }

  .gap-3 {
    gap: calc(var(--spacing) * 3);
  }

  .gap-4 {
    gap: calc(var(--spacing) * 4);
  }

  .gap-6 {
    gap: calc(var(--spacing) * 6);
  }

  :where(.space-y-3 > :not(:last-child)) {
    --tw-space-y-reverse: 0;
    margin-block-start: calc(calc(var(--spacing) * 3) * var(--tw-space-y-reverse));
    margin-block-end: calc(calc(var(--spacing) * 3) * calc(1 - var(--tw-space-y-reverse)));
  }

  :where(.space-y-5 > :not(:last-child)) {
    --tw-space-y-reverse: 0;
    margin-block-start: calc(calc(var(--spacing) * 5) * var(--tw-space-y-reverse));
    margin-block-end: calc(calc(var(--spacing) * 5) * calc(1 - var(--tw-space-y-reverse)));
  }

  :where(.space-y-6 > :not(:last-child)) {
    --tw-space-y-reverse: 0;
    margin-block-start: calc(calc(var(--spacing) * 6) * var(--tw-space-y-reverse));
    margin-block-end: calc(calc(var(--spacing) * 6) * calc(1 - var(--tw-space-y-reverse)));
  }

  :where(.space-y-8 > :not(:last-child)) {
    --tw-space-y-reverse: 0;
    margin-block-start: calc(calc(var(--spacing) * 8) * var(--tw-space-y-reverse));
    margin-block-end: calc(calc(var(--spacing) * 8) * calc(1 - var(--tw-space-y-reverse)));
  }

  :where(.space-x-2 > :not(:last-child)) {
    --tw-space-x-reverse: 0;
    margin-inline-start: calc(calc(var(--spacing) * 2) * var(--tw-space-x-reverse));
    margin-inline-end: calc(calc(var(--spacing) * 2) * calc(1 - var(--tw-space-x-reverse)));
  }

  :where(.space-x-4 > :not(:last-child)) {
    --tw-space-x-reverse: 0;
    margin-inline-start: calc(calc(var(--spacing) * 4) * var(--tw-space-x-reverse));
    margin-inline-end: calc(calc(var(--spacing) * 4) * calc(1 - var(--tw-space-x-reverse)));
  }

  :where(.divide-y > :not(:last-child)) {
    --tw-divide-y-reverse: 0;
    border-bottom-style: var(--tw-border-style);
    border-top-style: var(--tw-border-style);
    border-top-width: calc(1px * var(--tw-divide-y-reverse));
    border-bottom-width: calc(1px * calc(1 - var(--tw-divide-y-reverse)));
  }

  :where(.divide-border\/50 > :not(:last-child)) {
    border-color: var(--border);
  }

  @supports (color: color-mix(in lab, red, red)) {
    :where(.divide-border\/50 > :not(:last-child)) {
      border-color: color-mix(in oklab, var(--border) 50%, transparent);
    }
  }

  .truncate {
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
  }

  .overflow-hidden {
    overflow: hidden;
  }

  .overflow-x-auto {
    overflow-x: auto;
  }

  .rounded {
    border-radius: var(--radius);
  }

  .rounded-2xl {
    border-radius: var(--radius-2xl);
  }

  .rounded-full {
    border-radius: 3.40282e38px;
  }

  .rounded-lg {
    border-radius: var(--radius-lg);
  }

  .rounded-md {
    border-radius: var(--radius-md);
  }

  .rounded-xl {
    border-radius: var(--radius-xl);
  }

  .border {
    border-style: var(--tw-border-style);
    border-width: 1px;
  }

  .border-2 {
    border-style: var(--tw-border-style);
    border-width: 2px;
  }

  .border-t {
    border-top-style: var(--tw-border-style);
    border-top-width: 1px;
  }

  .border-b {
    border-bottom-style: var(--tw-border-style);
    border-bottom-width: 1px;
  }

  .border-border {
    border-color: var(--border);
  }

  .border-current {
    border-color: currentColor;
  }

  .border-destructive\/20 {
    border-color: var(--destructive);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .border-destructive\/20 {
      border-color: color-mix(in oklab, var(--destructive) 20%, transparent);
    }
  }

  .border-green-200 {
    border-color: var(--color-green-200);
  }

  .border-input {
    border-color: var(--input);
  }

  .border-t-transparent {
    border-top-color: #0000;
  }

  .bg-background {
    background-color: var(--background);
  }

  .bg-blue-100 {
    background-color: var(--color-blue-100);
  }

  .bg-card {
    background-color: var(--card);
  }

  .bg-card\/95 {
    background-color: var(--card);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-card\/95 {
      background-color: color-mix(in oklab, var(--card) 95%, transparent);
    }
  }

  .bg-destructive {
    background-color: var(--destructive);
  }

  .bg-destructive\/10 {
    background-color: var(--destructive);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-destructive\/10 {
      background-color: color-mix(in oklab, var(--destructive) 10%, transparent);
    }
  }

  .bg-green-50 {
    background-color: var(--color-green-50);
  }

  .bg-green-100 {
    background-color: var(--color-green-100);
  }

  .bg-muted {
    background-color: var(--muted);
  }

  .bg-muted\/20 {
    background-color: var(--muted);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-muted\/20 {
      background-color: color-mix(in oklab, var(--muted) 20%, transparent);
    }
  }

  .bg-muted\/30 {
    background-color: var(--muted);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-muted\/30 {
      background-color: color-mix(in oklab, var(--muted) 30%, transparent);
    }
  }

  .bg-muted\/50 {
    background-color: var(--muted);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-muted\/50 {
      background-color: color-mix(in oklab, var(--muted) 50%, transparent);
    }
  }

  .bg-primary {
    background-color: var(--primary);
  }

  .bg-primary\/10 {
    background-color: var(--primary);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-primary\/10 {
      background-color: color-mix(in oklab, var(--primary) 10%, transparent);
    }
  }

  .bg-secondary {
    background-color: var(--secondary);
  }

  .bg-gradient-to-br {
    --tw-gradient-position: to bottom right in oklab;
    background-image: linear-gradient(var(--tw-gradient-stops));
  }

  .bg-gradient-to-r {
    --tw-gradient-position: to right in oklab;
    background-image: linear-gradient(var(--tw-gradient-stops));
  }

  .bg-\[radial-gradient\(circle_at_30\%_20\%\,rgba\(120\,119\,198\,0\.1\)\,transparent_50\%\)\] {
    background-image: radial-gradient(circle at 30% 20%, #7877c61a, #0000 50%);
  }

  .from-muted\/50 {
    --tw-gradient-from: var(--muted);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .from-muted\/50 {
      --tw-gradient-from: color-mix(in oklab, var(--muted) 50%, transparent);
    }
  }

  .from-primary\/5 {
    --tw-gradient-from: var(--primary);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .from-primary\/5 {
      --tw-gradient-from: color-mix(in oklab, var(--primary) 5%, transparent);
    }
  }

  .via-transparent {
    --tw-gradient-via: transparent;
    --tw-gradient-via-stops: var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-via) var(--tw-gradient-via-position), var(--tw-gradient-to) var(--tw-gradient-to-position);
    --tw-gradient-stops: var(--tw-gradient-via-stops);
  }

  .to-muted\/30 {
    --tw-gradient-to: var(--muted);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .to-muted\/30 {
      --tw-gradient-to: color-mix(in oklab, var(--muted) 30%, transparent);
    }
  }

  .to-secondary\/5 {
    --tw-gradient-to: var(--secondary);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .to-secondary\/5 {
      --tw-gradient-to: color-mix(in oklab, var(--secondary) 5%, transparent);
    }
  }

  .p-3 {
    padding: calc(var(--spacing) * 3);
  }

  .p-4 {
    padding: calc(var(--spacing) * 4);
  }

  .p-6 {
    padding: calc(var(--spacing) * 6);
  }

  .p-8 {
    padding: calc(var(--spacing) * 8);
  }

  .px-1 {
    padding-inline: calc(var(--spacing) * 1);
  }

  .px-2 {
    padding-inline: calc(var(--spacing) * 2);
  }

  .px-3 {
    padding-inline: calc(var(--spacing) * 3);
  }

  .px-4 {
    padding-inline: calc(var(--spacing) * 4);
  }

  .px-6 {
    padding-inline: calc(var(--spacing) * 6);
  }

  .px-8 {
    padding-inline: calc(var(--spacing) * 8);
  }

  .py-1 {
    padding-block: calc(var(--spacing) * 1);
  }

  .py-2 {
    padding-block: calc(var(--spacing) * 2);
  }

  .py-3 {
    padding-block: calc(var(--spacing) * 3);
  }

  .py-4 {
    padding-block: calc(var(--spacing) * 4);
  }

  .py-6 {
    padding-block: calc(var(--spacing) * 6);
  }

  .py-8 {
    padding-block: calc(var(--spacing) * 8);
  }

  .py-12 {
    padding-block: calc(var(--spacing) * 12);
  }

  .py-16 {
    padding-block: calc(var(--spacing) * 16);
  }

  .pt-4 {
    padding-top: calc(var(--spacing) * 4);
  }

  .text-center {
    text-align: center;
  }

  .text-left {
    text-align: left;
  }

  .text-2xl {
    font-size: var(--text-2xl);
    line-height: var(--tw-leading, var(--text-2xl--line-height));
  }

  .text-base {
    font-size: var(--text-base);
    line-height: var(--tw-leading, var(--text-base--line-height));
  }

  .text-lg {
    font-size: var(--text-lg);
    line-height: var(--tw-leading, var(--text-lg--line-height));
  }

  .text-sm {
    font-size: var(--text-sm);
    line-height: var(--tw-leading, var(--text-sm--line-height));
  }

  .text-xl {
    font-size: var(--text-xl);
    line-height: var(--tw-leading, var(--text-xl--line-height));
  }

  .text-xs {
    font-size: var(--text-xs);
    line-height: var(--tw-leading, var(--text-xs--line-height));
  }

  .leading-relaxed {
    --tw-leading: var(--leading-relaxed);
    line-height: var(--leading-relaxed);
  }

  .leading-tight {
    --tw-leading: var(--leading-tight);
    line-height: var(--leading-tight);
  }

  .font-bold {
    --tw-font-weight: var(--font-weight-bold);
    font-weight: var(--font-weight-bold);
  }

  .font-medium {
    --tw-font-weight: var(--font-weight-medium);
    font-weight: var(--font-weight-medium);
  }

  .font-semibold {
    --tw-font-weight: var(--font-weight-semibold);
    font-weight: var(--font-weight-semibold);
  }

  .tracking-tight {
    --tw-tracking: var(--tracking-tight);
    letter-spacing: var(--tracking-tight);
  }

  .tracking-wider {
    --tw-tracking: var(--tracking-wider);
    letter-spacing: var(--tracking-wider);
  }

  .whitespace-nowrap {
    white-space: nowrap;
  }

  .text-blue-600 {
    color: var(--color-blue-600);
  }

  .text-card-foreground {
    color: var(--card-foreground);
  }

  .text-destructive {
    color: var(--destructive);
  }

  .text-destructive-foreground {
    color: var(--destructive-foreground);
  }

  .text-foreground {
    color: var(--foreground);
  }

  .text-green-600 {
    color: var(--color-green-600);
  }

  .text-green-700 {
    color: var(--color-green-700);
  }

  .text-muted-foreground {
    color: var(--muted-foreground);
  }

  .text-primary {
    color: var(--primary);
  }

  .text-primary-foreground {
    color: var(--primary-foreground);
  }

  .text-red-600 {
    color: var(--color-red-600);
  }

  .text-secondary-foreground {
    color: var(--secondary-foreground);
  }

  .uppercase {
    text-transform: uppercase;
  }

  .antialiased {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  .shadow-lg {
    --tw-shadow: 0 10px 15px -3px var(--tw-shadow-color, #0000001a), 0 4px 6px -4px var(--tw-shadow-color, #0000001a);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .shadow-sm {
    --tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, #0000001a), 0 1px 2px -1px var(--tw-shadow-color, #0000001a);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .shadow-xl {
    --tw-shadow: 0 20px 25px -5px var(--tw-shadow-color, #0000001a), 0 8px 10px -6px var(--tw-shadow-color, #0000001a);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .ring-offset-background {
    --tw-ring-offset-color: var(--background);
  }

  .backdrop-blur-sm {
    --tw-backdrop-blur: blur(var(--blur-sm));
    backdrop-filter: var(--tw-backdrop-blur, ) var(--tw-backdrop-brightness, ) var(--tw-backdrop-contrast, ) var(--tw-backdrop-grayscale, ) var(--tw-backdrop-hue-rotate, ) var(--tw-backdrop-invert, ) var(--tw-backdrop-opacity, ) var(--tw-backdrop-saturate, ) var(--tw-backdrop-sepia, );
  }

  .transition-all {
    transition-property: all;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }

  .transition-colors {
    transition-property: color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }

  .transition-transform {
    transition-property: transform, translate, scale, rotate;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }

  .duration-200 {
    --tw-duration: .2s;
    transition-duration: .2s;
  }

  .duration-300 {
    --tw-duration: .3s;
    transition-duration: .3s;
  }

  @media (hover: hover) {
    .group-hover\:-translate-x-1:is(:where(.group):hover *) {
      --tw-translate-x: calc(var(--spacing) * -1);
      translate: var(--tw-translate-x) var(--tw-translate-y);
    }
  }

  @media (hover: hover) {
    .group-hover\:-translate-y-1:is(:where(.group):hover *) {
      --tw-translate-y: calc(var(--spacing) * -1);
      translate: var(--tw-translate-x) var(--tw-translate-y);
    }
  }

  @media (hover: hover) {
    .group-hover\:scale-110:is(:where(.group):hover *) {
      --tw-scale-x: 110%;
      --tw-scale-y: 110%;
      --tw-scale-z: 110%;
      scale: var(--tw-scale-x) var(--tw-scale-y);
    }
  }

  @media (hover: hover) {
    .group-hover\:bg-primary\/20:is(:where(.group):hover *) {
      background-color: var(--primary);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .group-hover\:bg-primary\/20:is(:where(.group):hover *) {
        background-color: color-mix(in oklab, var(--primary) 20%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .group-hover\:text-primary:is(:where(.group):hover *) {
      color: var(--primary);
    }
  }

  @media (hover: hover) {
    .group-hover\/action\:scale-110:is(:where(.group\/action):hover *) {
      --tw-scale-x: 110%;
      --tw-scale-y: 110%;
      --tw-scale-z: 110%;
      scale: var(--tw-scale-x) var(--tw-scale-y);
    }
  }

  @media (hover: hover) {
    .group-hover\/link\:translate-x-1:is(:where(.group\/link):hover *) {
      --tw-translate-x: calc(var(--spacing) * 1);
      translate: var(--tw-translate-x) var(--tw-translate-y);
    }
  }

  .placeholder\:text-muted-foreground::placeholder {
    color: var(--muted-foreground);
  }

  .placeholder\:text-muted-foreground\/60::placeholder {
    color: var(--muted-foreground);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .placeholder\:text-muted-foreground\/60::placeholder {
      color: color-mix(in oklab, var(--muted-foreground) 60%, transparent);
    }
  }

  @media (hover: hover) {
    .hover\:scale-110:hover {
      --tw-scale-x: 110%;
      --tw-scale-y: 110%;
      --tw-scale-z: 110%;
      scale: var(--tw-scale-x) var(--tw-scale-y);
    }
  }

  @media (hover: hover) {
    .hover\:border-primary\/20:hover {
      border-color: var(--primary);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .hover\:border-primary\/20:hover {
        border-color: color-mix(in oklab, var(--primary) 20%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .hover\:border-primary\/50:hover {
      border-color: var(--primary);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .hover\:border-primary\/50:hover {
        border-color: color-mix(in oklab, var(--primary) 50%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .hover\:bg-accent:hover {
      background-color: var(--accent);
    }
  }

  @media (hover: hover) {
    .hover\:bg-blue-50:hover {
      background-color: var(--color-blue-50);
    }
  }

  @media (hover: hover) {
    .hover\:bg-destructive\/10:hover {
      background-color: var(--destructive);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .hover\:bg-destructive\/10:hover {
        background-color: color-mix(in oklab, var(--destructive) 10%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .hover\:bg-destructive\/90:hover {
      background-color: var(--destructive);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .hover\:bg-destructive\/90:hover {
        background-color: color-mix(in oklab, var(--destructive) 90%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .hover\:bg-green-50:hover {
      background-color: var(--color-green-50);
    }
  }

  @media (hover: hover) {
    .hover\:bg-muted:hover {
      background-color: var(--muted);
    }
  }

  @media (hover: hover) {
    .hover\:bg-muted\/50:hover {
      background-color: var(--muted);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .hover\:bg-muted\/50:hover {
        background-color: color-mix(in oklab, var(--muted) 50%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .hover\:bg-primary\/90:hover {
      background-color: var(--primary);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .hover\:bg-primary\/90:hover {
        background-color: color-mix(in oklab, var(--primary) 90%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .hover\:bg-red-50:hover {
      background-color: var(--color-red-50);
    }
  }

  @media (hover: hover) {
    .hover\:bg-secondary\/80:hover {
      background-color: var(--secondary);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .hover\:bg-secondary\/80:hover {
        background-color: color-mix(in oklab, var(--secondary) 80%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .hover\:bg-gradient-to-r:hover {
      --tw-gradient-position: to right in oklab;
      background-image: linear-gradient(var(--tw-gradient-stops));
    }
  }

  @media (hover: hover) {
    .hover\:from-muted\/20:hover {
      --tw-gradient-from: var(--muted);
      --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
    }

    @supports (color: color-mix(in lab, red, red)) {
      .hover\:from-muted\/20:hover {
        --tw-gradient-from: color-mix(in oklab, var(--muted) 20%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .hover\:to-transparent:hover {
      --tw-gradient-to: transparent;
      --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
    }
  }

  @media (hover: hover) {
    .hover\:text-accent-foreground:hover {
      color: var(--accent-foreground);
    }
  }

  @media (hover: hover) {
    .hover\:text-blue-700:hover {
      color: var(--color-blue-700);
    }
  }

  @media (hover: hover) {
    .hover\:text-destructive:hover {
      color: var(--destructive);
    }
  }

  @media (hover: hover) {
    .hover\:text-foreground:hover {
      color: var(--foreground);
    }
  }

  @media (hover: hover) {
    .hover\:text-green-700:hover {
      color: var(--color-green-700);
    }
  }

  @media (hover: hover) {
    .hover\:text-primary:hover {
      color: var(--primary);
    }
  }

  @media (hover: hover) {
    .hover\:text-primary\/80:hover {
      color: var(--primary);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .hover\:text-primary\/80:hover {
        color: color-mix(in oklab, var(--primary) 80%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .hover\:text-red-700:hover {
      color: var(--color-red-700);
    }
  }

  @media (hover: hover) {
    .hover\:shadow-lg:hover {
      --tw-shadow: 0 10px 15px -3px var(--tw-shadow-color, #0000001a), 0 4px 6px -4px var(--tw-shadow-color, #0000001a);
      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    }
  }

  @media (hover: hover) {
    .hover\:shadow-md:hover {
      --tw-shadow: 0 4px 6px -1px var(--tw-shadow-color, #0000001a), 0 2px 4px -2px var(--tw-shadow-color, #0000001a);
      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    }
  }

  @media (hover: hover) {
    .hover\:shadow-xl:hover {
      --tw-shadow: 0 20px 25px -5px var(--tw-shadow-color, #0000001a), 0 8px 10px -6px var(--tw-shadow-color, #0000001a);
      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    }
  }

  .focus\:border-primary:focus {
    border-color: var(--primary);
  }

  .focus\:border-transparent:focus {
    border-color: #0000;
  }

  .focus\:ring-2:focus {
    --tw-ring-shadow: var(--tw-ring-inset, ) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .focus\:ring-primary:focus {
    --tw-ring-color: var(--primary);
  }

  .focus\:ring-primary\/20:focus {
    --tw-ring-color: var(--primary);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .focus\:ring-primary\/20:focus {
      --tw-ring-color: color-mix(in oklab, var(--primary) 20%, transparent);
    }
  }

  .focus\:ring-ring:focus {
    --tw-ring-color: var(--ring);
  }

  .focus\:ring-offset-2:focus {
    --tw-ring-offset-width: 2px;
    --tw-ring-offset-shadow: var(--tw-ring-inset, ) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  }

  .focus\:outline-none:focus {
    --tw-outline-style: none;
    outline-style: none;
  }

  .focus-visible\:ring-2:focus-visible {
    --tw-ring-shadow: var(--tw-ring-inset, ) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .focus-visible\:ring-ring:focus-visible {
    --tw-ring-color: var(--ring);
  }

  .focus-visible\:ring-offset-2:focus-visible {
    --tw-ring-offset-width: 2px;
    --tw-ring-offset-shadow: var(--tw-ring-inset, ) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  }

  .focus-visible\:outline-none:focus-visible {
    --tw-outline-style: none;
    outline-style: none;
  }

  .disabled\:pointer-events-none:disabled {
    pointer-events: none;
  }

  .disabled\:cursor-not-allowed:disabled {
    cursor: not-allowed;
  }

  .disabled\:opacity-50:disabled {
    opacity: .5;
  }

  @media (width >= 40rem) {
    .sm\:mt-8 {
      margin-top: calc(var(--spacing) * 8);
    }
  }

  @media (width >= 40rem) {
    .sm\:mr-2 {
      margin-right: calc(var(--spacing) * 2);
    }
  }

  @media (width >= 40rem) {
    .sm\:mb-6 {
      margin-bottom: calc(var(--spacing) * 6);
    }
  }

  @media (width >= 40rem) {
    .sm\:mb-8 {
      margin-bottom: calc(var(--spacing) * 8);
    }
  }

  @media (width >= 40rem) {
    .sm\:block {
      display: block;
    }
  }

  @media (width >= 40rem) {
    .sm\:hidden {
      display: none;
    }
  }

  @media (width >= 40rem) {
    .sm\:inline {
      display: inline;
    }
  }

  @media (width >= 40rem) {
    .sm\:table-cell {
      display: table-cell;
    }
  }

  @media (width >= 40rem) {
    .sm\:h-4 {
      height: calc(var(--spacing) * 4);
    }
  }

  @media (width >= 40rem) {
    .sm\:w-4 {
      width: calc(var(--spacing) * 4);
    }
  }

  @media (width >= 40rem) {
    .sm\:w-auto {
      width: auto;
    }
  }

  @media (width >= 40rem) {
    .sm\:max-w-md {
      max-width: var(--container-md);
    }
  }

  @media (width >= 40rem) {
    .sm\:flex-row {
      flex-direction: row;
    }
  }

  @media (width >= 40rem) {
    .sm\:items-center {
      align-items: center;
    }
  }

  @media (width >= 40rem) {
    .sm\:justify-between {
      justify-content: space-between;
    }
  }

  @media (width >= 40rem) {
    .sm\:gap-4 {
      gap: calc(var(--spacing) * 4);
    }
  }

  @media (width >= 40rem) {
    .sm\:p-6 {
      padding: calc(var(--spacing) * 6);
    }
  }

  @media (width >= 40rem) {
    .sm\:p-8 {
      padding: calc(var(--spacing) * 8);
    }
  }

  @media (width >= 40rem) {
    .sm\:px-3 {
      padding-inline: calc(var(--spacing) * 3);
    }
  }

  @media (width >= 40rem) {
    .sm\:px-4 {
      padding-inline: calc(var(--spacing) * 4);
    }
  }

  @media (width >= 40rem) {
    .sm\:px-6 {
      padding-inline: calc(var(--spacing) * 6);
    }
  }

  @media (width >= 40rem) {
    .sm\:py-2 {
      padding-block: calc(var(--spacing) * 2);
    }
  }

  @media (width >= 40rem) {
    .sm\:py-6 {
      padding-block: calc(var(--spacing) * 6);
    }
  }

  @media (width >= 40rem) {
    .sm\:text-2xl {
      font-size: var(--text-2xl);
      line-height: var(--tw-leading, var(--text-2xl--line-height));
    }
  }

  @media (width >= 40rem) {
    .sm\:text-3xl {
      font-size: var(--text-3xl);
      line-height: var(--tw-leading, var(--text-3xl--line-height));
    }
  }

  @media (width >= 40rem) {
    .sm\:text-sm {
      font-size: var(--text-sm);
      line-height: var(--tw-leading, var(--text-sm--line-height));
    }
  }

  @media (width >= 48rem) {
    .md\:grid-cols-2 {
      grid-template-columns: repeat(2, minmax(0, 1fr));
    }
  }

  @media (width >= 48rem) {
    .md\:grid-cols-3 {
      grid-template-columns: repeat(3, minmax(0, 1fr));
    }
  }

  @media (width >= 64rem) {
    .lg\:mb-8 {
      margin-bottom: calc(var(--spacing) * 8);
    }
  }

  @media (width >= 64rem) {
    .lg\:mb-12 {
      margin-bottom: calc(var(--spacing) * 12);
    }
  }

  @media (width >= 64rem) {
    .lg\:table-cell {
      display: table-cell;
    }
  }

  @media (width >= 64rem) {
    .lg\:grid-cols-3 {
      grid-template-columns: repeat(3, minmax(0, 1fr));
    }
  }

  @media (width >= 64rem) {
    .lg\:flex-row {
      flex-direction: row;
    }
  }

  @media (width >= 64rem) {
    .lg\:items-start {
      align-items: flex-start;
    }
  }

  @media (width >= 64rem) {
    .lg\:justify-between {
      justify-content: space-between;
    }
  }

  @media (width >= 64rem) {
    .lg\:gap-6 {
      gap: calc(var(--spacing) * 6);
    }
  }

  @media (width >= 64rem) {
    .lg\:gap-8 {
      gap: calc(var(--spacing) * 8);
    }
  }

  @media (width >= 64rem) {
    :where(.lg\:space-y-8 > :not(:last-child)) {
      --tw-space-y-reverse: 0;
      margin-block-start: calc(calc(var(--spacing) * 8) * var(--tw-space-y-reverse));
      margin-block-end: calc(calc(var(--spacing) * 8) * calc(1 - var(--tw-space-y-reverse)));
    }
  }

  @media (width >= 64rem) {
    .lg\:p-8 {
      padding: calc(var(--spacing) * 8);
    }
  }

  @media (width >= 64rem) {
    .lg\:px-6 {
      padding-inline: calc(var(--spacing) * 6);
    }
  }

  @media (width >= 64rem) {
    .lg\:px-8 {
      padding-inline: calc(var(--spacing) * 8);
    }
  }

  @media (width >= 64rem) {
    .lg\:py-6 {
      padding-block: calc(var(--spacing) * 6);
    }
  }

  @media (width >= 64rem) {
    .lg\:py-8 {
      padding-block: calc(var(--spacing) * 8);
    }
  }

  @media (width >= 64rem) {
    .lg\:py-12 {
      padding-block: calc(var(--spacing) * 12);
    }
  }

  @media (width >= 64rem) {
    .lg\:py-24 {
      padding-block: calc(var(--spacing) * 24);
    }
  }

  @media (width >= 64rem) {
    .lg\:text-2xl {
      font-size: var(--text-2xl);
      line-height: var(--tw-leading, var(--text-2xl--line-height));
    }
  }

  @media (width >= 64rem) {
    .lg\:text-3xl {
      font-size: var(--text-3xl);
      line-height: var(--tw-leading, var(--text-3xl--line-height));
    }
  }

  @media (width >= 64rem) {
    .lg\:text-4xl {
      font-size: var(--text-4xl);
      line-height: var(--tw-leading, var(--text-4xl--line-height));
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:border-green-800 {
      border-color: var(--color-green-800);
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:bg-blue-900\/20 {
      background-color: #1c398e33;
    }

    @supports (color: color-mix(in lab, red, red)) {
      .dark\:bg-blue-900\/20 {
        background-color: color-mix(in oklab, var(--color-blue-900) 20%, transparent);
      }
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:bg-green-900\/20 {
      background-color: #0d542b33;
    }

    @supports (color: color-mix(in lab, red, red)) {
      .dark\:bg-green-900\/20 {
        background-color: color-mix(in oklab, var(--color-green-900) 20%, transparent);
      }
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:text-blue-400 {
      color: var(--color-blue-400);
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:text-green-400 {
      color: var(--color-green-400);
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:text-red-400 {
      color: var(--color-red-400);
    }
  }

  @media (prefers-color-scheme: dark) {
    @media (hover: hover) {
      .dark\:hover\:bg-blue-900\/20:hover {
        background-color: #1c398e33;
      }

      @supports (color: color-mix(in lab, red, red)) {
        .dark\:hover\:bg-blue-900\/20:hover {
          background-color: color-mix(in oklab, var(--color-blue-900) 20%, transparent);
        }
      }
    }
  }

  @media (prefers-color-scheme: dark) {
    @media (hover: hover) {
      .dark\:hover\:bg-green-900\/20:hover {
        background-color: #0d542b33;
      }

      @supports (color: color-mix(in lab, red, red)) {
        .dark\:hover\:bg-green-900\/20:hover {
          background-color: color-mix(in oklab, var(--color-green-900) 20%, transparent);
        }
      }
    }
  }

  @media (prefers-color-scheme: dark) {
    @media (hover: hover) {
      .dark\:hover\:bg-red-900\/20:hover {
        background-color: #82181a33;
      }

      @supports (color: color-mix(in lab, red, red)) {
        .dark\:hover\:bg-red-900\/20:hover {
          background-color: color-mix(in oklab, var(--color-red-900) 20%, transparent);
        }
      }
    }
  }

  @media (prefers-color-scheme: dark) {
    @media (hover: hover) {
      .dark\:hover\:text-blue-300:hover {
        color: var(--color-blue-300);
      }
    }
  }

  @media (prefers-color-scheme: dark) {
    @media (hover: hover) {
      .dark\:hover\:text-green-300:hover {
        color: var(--color-green-300);
      }
    }
  }

  @media (prefers-color-scheme: dark) {
    @media (hover: hover) {
      .dark\:hover\:text-red-300:hover {
        color: var(--color-red-300);
      }
    }
  }
}

.w-md-editor-bar {
  cursor: s-resize;
  z-index: 3;
  -webkit-user-select: none;
  user-select: none;
  border-radius: 0 0 3px;
  width: 14px;
  height: 10px;
  margin-top: -11px;
  margin-right: 0;
  position: absolute;
  bottom: 0;
  right: 0;
}

.w-md-editor-bar svg {
  margin: 0 auto;
  display: block;
}

.w-md-editor-area {
  border-radius: 5px;
  overflow: auto;
}

.w-md-editor-text {
  text-align: left;
  white-space: pre-wrap;
  word-break: keep-all;
  overflow-wrap: break-word;
  box-sizing: border-box;
  font-variant-ligatures: common-ligatures;
  min-height: 100%;
  margin: 0;
  padding: 10px;
  position: relative;
  font-size: 14px !important;
  line-height: 18px !important;
}

.w-md-editor-text-pre, .w-md-editor-text-input, .w-md-editor-text > .w-md-editor-text-pre {
  box-sizing: inherit;
  display: inherit;
  font-family: inherit;
  font-size: inherit;
  font-style: inherit;
  font-variant-ligatures: inherit;
  font-weight: inherit;
  letter-spacing: inherit;
  line-height: inherit;
  tab-size: inherit;
  text-indent: inherit;
  text-rendering: inherit;
  text-transform: inherit;
  white-space: inherit;
  overflow-wrap: inherit;
  word-break: inherit;
  word-break: normal;
  background: none;
  border: 0;
  margin: 0;
  padding: 0;
  font-family: var(--md-editor-font-family) !important;
}

.w-md-editor-text-pre {
  pointer-events: none;
  position: relative;
  background-color: #0000 !important;
  margin: 0 !important;
}

.w-md-editor-text-pre > code {
  font-family: var(--md-editor-font-family) !important;
  padding: 0 !important;
  font-size: 14px !important;
  line-height: 18px !important;
}

.w-md-editor-text-input {
  resize: none;
  width: 100%;
  height: 100%;
  color: inherit;
  padding: inherit;
  -webkit-font-smoothing: antialiased;
  -webkit-text-fill-color: transparent;
  outline: 0;
  position: absolute;
  top: 0;
  left: 0;
  overflow: hidden;
}

.w-md-editor-text-input:empty {
  -webkit-text-fill-color: inherit !important;
}

.w-md-editor-text-pre, .w-md-editor-text-input {
  word-wrap: pre;
  word-break: break-word;
  white-space: pre-wrap;
}

@media (-ms-high-contrast: none), (-ms-high-contrast: active) {
  .w-md-editor-text-input {
    color: #0000 !important;
  }

  .w-md-editor-text-input::selection {
    color: #0000 !important;
    background-color: #accef7 !important;
  }
}

.w-md-editor-text-pre .punctuation {
  color: var(--color-prettylights-syntax-comment, #8b949e) !important;
}

.w-md-editor-text-pre .token.url, .w-md-editor-text-pre .token.content {
  color: var(--color-prettylights-syntax-constant, #0550ae) !important;
}

.w-md-editor-text-pre .token.title.important {
  color: var(--color-prettylights-syntax-markup-bold, #24292f);
}

.w-md-editor-text-pre .token.code-block .function {
  color: var(--color-prettylights-syntax-entity, #8250df);
}

.w-md-editor-text-pre .token.bold {
  font-weight: unset !important;
}

.w-md-editor-text-pre .token.title {
  line-height: unset !important;
  font-size: unset !important;
  font-weight: unset !important;
}

.w-md-editor-text-pre .token.code.keyword {
  color: var(--color-prettylights-syntax-constant, #0550ae) !important;
}

.w-md-editor-text-pre .token.strike, .w-md-editor-text-pre .token.strike .content {
  color: var(--color-prettylights-syntax-markup-deleted-text, #82071e) !important;
}

.w-md-editor-toolbar-child {
  box-shadow: 0 0 0 1px var(--md-editor-box-shadow-color), 0 0 0 var(--md-editor-box-shadow-color), 0 1px 1px var(--md-editor-box-shadow-color);
  background-color: var(--md-editor-background-color);
  z-index: 1;
  border-radius: 3px;
  display: none;
  position: absolute;
}

.w-md-editor-toolbar-child.active {
  display: block;
}

.w-md-editor-toolbar-child .w-md-editor-toolbar {
  border-bottom: 0;
  border-radius: 3px;
  padding: 3px;
}

.w-md-editor-toolbar-child .w-md-editor-toolbar ul > li {
  display: block;
}

.w-md-editor-toolbar-child .w-md-editor-toolbar ul > li button {
  width: -webkit-fill-available;
  height: initial;
  box-sizing: border-box;
  margin: 0;
  padding: 3px 4px 2px;
}

.w-md-editor-toolbar {
  border-bottom: 1px solid var(--md-editor-box-shadow-color);
  background-color: var(--md-editor-background-color);
  -webkit-user-select: none;
  user-select: none;
  border-radius: 3px 3px 0 0;
  flex-wrap: wrap;
  justify-content: space-between;
  align-items: center;
  padding: 3px;
  display: flex;
}

.w-md-editor-toolbar.bottom {
  border-bottom: 0;
  border-top: 1px solid var(--md-editor-box-shadow-color);
  border-radius: 0 0 3px 3px;
}

.w-md-editor-toolbar ul, .w-md-editor-toolbar li {
  line-height: initial;
  margin: 0;
  padding: 0;
  list-style: none;
}

.w-md-editor-toolbar li {
  font-size: 14px;
  display: inline-block;
}

.w-md-editor-toolbar li + li {
  margin: 0;
}

.w-md-editor-toolbar li > button {
  text-transform: none;
  cursor: pointer;
  white-space: nowrap;
  height: 20px;
  color: var(--color-fg-default);
  background: none;
  border: none;
  border-radius: 2px;
  outline: none;
  margin: 0 1px;
  padding: 4px;
  font-weight: normal;
  line-height: 14px;
  transition: all .3s;
  overflow: visible;
}

.w-md-editor-toolbar li > button:hover, .w-md-editor-toolbar li > button:focus {
  background-color: var(--color-neutral-muted);
  color: var(--color-accent-fg);
}

.w-md-editor-toolbar li > button:active {
  background-color: var(--color-neutral-muted);
  color: var(--color-danger-fg);
}

.w-md-editor-toolbar li > button:disabled {
  color: var(--md-editor-box-shadow-color);
  cursor: not-allowed;
}

.w-md-editor-toolbar li > button:disabled:hover {
  color: var(--md-editor-box-shadow-color);
  background-color: #0000;
}

.w-md-editor-toolbar li.active > button {
  color: var(--color-accent-fg);
  background-color: var(--color-neutral-muted);
}

.w-md-editor-toolbar-divider {
  vertical-align: middle;
  background-color: var(--md-editor-box-shadow-color);
  width: 1px;
  height: 14px;
  margin: -3px 3px 0 !important;
}

.w-md-editor {
  text-align: left;
  color: var(--color-fg-default);
  --md-editor-font-family: "Helvetica Neue", Helvetica, Arial, sans-serif;
  --md-editor-background-color: var(--color-canvas-default, #fff);
  --md-editor-box-shadow-color: var(--color-border-default, #d0d7de);
  box-shadow: 0 0 0 1px var(--md-editor-box-shadow-color), 0 0 0 var(--md-editor-box-shadow-color), 0 1px 1px var(--md-editor-box-shadow-color);
  background-color: var(--md-editor-background-color);
  border-radius: 3px;
  flex-direction: column;
  padding-bottom: 1px;
  font-family: Helvetica Neue, Helvetica, Arial, sans-serif;
  display: flex;
  position: relative;
}

.w-md-editor.w-md-editor-rtl {
  text-align: right !important;
  direction: rtl !important;
}

.w-md-editor.w-md-editor-rtl .w-md-editor-preview {
  box-shadow: inset -1px 0 0 0 var(--md-editor-box-shadow-color);
  left: 0;
  right: unset !important;
  text-align: right !important;
}

.w-md-editor.w-md-editor-rtl .w-md-editor-text {
  text-align: right !important;
}

.w-md-editor-toolbar {
  height: fit-content;
}

.w-md-editor-content {
  border-radius: 0 0 3px;
  height: 100%;
  position: relative;
  overflow: auto;
}

.w-md-editor .copied {
  display: none !important;
}

.w-md-editor-input {
  width: 50%;
  height: 100%;
}

.w-md-editor-text-pre > code {
  word-break: break-word !important;
  white-space: pre-wrap !important;
}

.w-md-editor-preview {
  box-sizing: border-box;
  width: 50%;
  box-shadow: inset 1px 0 0 0 var(--md-editor-box-shadow-color);
  border-radius: 0 0 5px;
  flex-direction: column;
  padding: 10px 20px;
  display: flex;
  position: absolute;
  top: 0;
  bottom: 0;
  right: 0;
  overflow: auto;
}

.w-md-editor-preview .anchor {
  display: none;
}

.w-md-editor-preview .contains-task-list li.task-list-item {
  list-style: none;
}

.w-md-editor-show-preview .w-md-editor-input {
  background-color: var(--md-editor-background-color);
  width: 0%;
  overflow: hidden;
}

.w-md-editor-show-preview .w-md-editor-preview {
  width: 100%;
  box-shadow: inset 0 0;
}

.w-md-editor-show-edit .w-md-editor-input {
  width: 100%;
}

.w-md-editor-show-edit .w-md-editor-preview {
  width: 0%;
  padding: 0;
}

.w-md-editor-fullscreen {
  z-index: 99999;
  position: fixed;
  inset: 0;
  overflow: hidden;
  height: 100% !important;
}

.w-md-editor-fullscreen .w-md-editor-content {
  height: 100%;
}

@media (prefers-color-scheme: dark) {
  .wmde-markdown, .wmde-markdown-var {
    color-scheme: dark;
    --color-prettylights-syntax-comment: #8b949e;
    --color-prettylights-syntax-constant: #79c0ff;
    --color-prettylights-syntax-entity: #d2a8ff;
    --color-prettylights-syntax-storage-modifier-import: #c9d1d9;
    --color-prettylights-syntax-entity-tag: #7ee787;
    --color-prettylights-syntax-keyword: #ff7b72;
    --color-prettylights-syntax-string: #a5d6ff;
    --color-prettylights-syntax-variable: #ffa657;
    --color-prettylights-syntax-brackethighlighter-unmatched: #f85149;
    --color-prettylights-syntax-invalid-illegal-text: #f0f6fc;
    --color-prettylights-syntax-invalid-illegal-bg: #8e1519;
    --color-prettylights-syntax-carriage-return-text: #f0f6fc;
    --color-prettylights-syntax-carriage-return-bg: #b62324;
    --color-prettylights-syntax-string-regexp: #7ee787;
    --color-prettylights-syntax-markup-list: #f2cc60;
    --color-prettylights-syntax-markup-heading: #1f6feb;
    --color-prettylights-syntax-markup-italic: #c9d1d9;
    --color-prettylights-syntax-markup-bold: #c9d1d9;
    --color-prettylights-syntax-markup-deleted-text: #ffdcd7;
    --color-prettylights-syntax-markup-deleted-bg: #67060c;
    --color-prettylights-syntax-markup-inserted-text: #aff5b4;
    --color-prettylights-syntax-markup-inserted-bg: #033a16;
    --color-prettylights-syntax-markup-changed-text: #ffdfb6;
    --color-prettylights-syntax-markup-changed-bg: #5a1e02;
    --color-prettylights-syntax-markup-ignored-text: #c9d1d9;
    --color-prettylights-syntax-markup-ignored-bg: #1158c7;
    --color-prettylights-syntax-meta-diff-range: #d2a8ff;
    --color-prettylights-syntax-brackethighlighter-angle: #8b949e;
    --color-prettylights-syntax-sublimelinter-gutter-mark: #484f58;
    --color-prettylights-syntax-constant-other-reference-link: #a5d6ff;
    --color-fg-default: #c9d1d9;
    --color-fg-muted: #8b949e;
    --color-fg-subtle: #484f58;
    --color-canvas-default: #0d1117;
    --color-canvas-subtle: #161b22;
    --color-border-default: #30363d;
    --color-border-muted: #21262d;
    --color-neutral-muted: #6e768166;
    --color-accent-fg: #58a6ff;
    --color-accent-emphasis: #1f6feb;
    --color-attention-subtle: #bb800926;
    --color-danger-fg: #f85149;
    --color-danger-emphasis: #da3633;
    --color-attention-fg: #d29922;
    --color-attention-emphasis: #9e6a03;
    --color-done-fg: #a371f7;
    --color-done-emphasis: #8957e5;
    --color-success-fg: #3fb950;
    --color-success-emphasis: #238636;
    --color-copied-active-bg: #2e9b33;
  }
}

@media (prefers-color-scheme: light) {
  .wmde-markdown, .wmde-markdown-var {
    color-scheme: light;
    --color-prettylights-syntax-comment: #6e7781;
    --color-prettylights-syntax-constant: #0550ae;
    --color-prettylights-syntax-entity: #8250df;
    --color-prettylights-syntax-storage-modifier-import: #24292f;
    --color-prettylights-syntax-entity-tag: #116329;
    --color-prettylights-syntax-keyword: #cf222e;
    --color-prettylights-syntax-string: #0a3069;
    --color-prettylights-syntax-variable: #953800;
    --color-prettylights-syntax-brackethighlighter-unmatched: #82071e;
    --color-prettylights-syntax-invalid-illegal-text: #f6f8fa;
    --color-prettylights-syntax-invalid-illegal-bg: #82071e;
    --color-prettylights-syntax-carriage-return-text: #f6f8fa;
    --color-prettylights-syntax-carriage-return-bg: #cf222e;
    --color-prettylights-syntax-string-regexp: #116329;
    --color-prettylights-syntax-markup-list: #3b2300;
    --color-prettylights-syntax-markup-heading: #0550ae;
    --color-prettylights-syntax-markup-italic: #24292f;
    --color-prettylights-syntax-markup-bold: #24292f;
    --color-prettylights-syntax-markup-deleted-text: #82071e;
    --color-prettylights-syntax-markup-deleted-bg: #ffebe9;
    --color-prettylights-syntax-markup-inserted-text: #116329;
    --color-prettylights-syntax-markup-inserted-bg: #dafbe1;
    --color-prettylights-syntax-markup-changed-text: #953800;
    --color-prettylights-syntax-markup-changed-bg: #ffd8b5;
    --color-prettylights-syntax-markup-ignored-text: #eaeef2;
    --color-prettylights-syntax-markup-ignored-bg: #0550ae;
    --color-prettylights-syntax-meta-diff-range: #8250df;
    --color-prettylights-syntax-brackethighlighter-angle: #57606a;
    --color-prettylights-syntax-sublimelinter-gutter-mark: #8c959f;
    --color-prettylights-syntax-constant-other-reference-link: #0a3069;
    --color-fg-default: #24292f;
    --color-fg-muted: #57606a;
    --color-fg-subtle: #6e7781;
    --color-canvas-default: #fff;
    --color-canvas-subtle: #f6f8fa;
    --color-border-default: #d0d7de;
    --color-border-muted: #d8dee4;
    --color-neutral-muted: #afb8c133;
    --color-accent-fg: #0969da;
    --color-accent-emphasis: #0969da;
    --color-attention-subtle: #fff8c5;
    --color-danger-fg: #d1242f;
    --color-danger-emphasis: #cf222e;
    --color-attention-fg: #9a6700;
    --color-attention-emphasis: #9a6700;
    --color-done-fg: #8250df;
    --color-done-emphasis: #8250df;
    --color-success-fg: #1a7f37;
    --color-success-emphasis: #1f883d;
    --color-copied-active-bg: #2e9b33;
  }
}

[data-color-mode*="dark"] .wmde-markdown, [data-color-mode*="dark"] .wmde-markdown-var, .wmde-markdown-var[data-color-mode*="dark"], .wmde-markdown[data-color-mode*="dark"], body[data-color-mode*="dark"] {
  color-scheme: dark;
  --color-prettylights-syntax-comment: #8b949e;
  --color-prettylights-syntax-constant: #79c0ff;
  --color-prettylights-syntax-entity: #d2a8ff;
  --color-prettylights-syntax-storage-modifier-import: #c9d1d9;
  --color-prettylights-syntax-entity-tag: #7ee787;
  --color-prettylights-syntax-keyword: #ff7b72;
  --color-prettylights-syntax-string: #a5d6ff;
  --color-prettylights-syntax-variable: #ffa657;
  --color-prettylights-syntax-brackethighlighter-unmatched: #f85149;
  --color-prettylights-syntax-invalid-illegal-text: #f0f6fc;
  --color-prettylights-syntax-invalid-illegal-bg: #8e1519;
  --color-prettylights-syntax-carriage-return-text: #f0f6fc;
  --color-prettylights-syntax-carriage-return-bg: #b62324;
  --color-prettylights-syntax-string-regexp: #7ee787;
  --color-prettylights-syntax-markup-list: #f2cc60;
  --color-prettylights-syntax-markup-heading: #1f6feb;
  --color-prettylights-syntax-markup-italic: #c9d1d9;
  --color-prettylights-syntax-markup-bold: #c9d1d9;
  --color-prettylights-syntax-markup-deleted-text: #ffdcd7;
  --color-prettylights-syntax-markup-deleted-bg: #67060c;
  --color-prettylights-syntax-markup-inserted-text: #aff5b4;
  --color-prettylights-syntax-markup-inserted-bg: #033a16;
  --color-prettylights-syntax-markup-changed-text: #ffdfb6;
  --color-prettylights-syntax-markup-changed-bg: #5a1e02;
  --color-prettylights-syntax-markup-ignored-text: #c9d1d9;
  --color-prettylights-syntax-markup-ignored-bg: #1158c7;
  --color-prettylights-syntax-meta-diff-range: #d2a8ff;
  --color-prettylights-syntax-brackethighlighter-angle: #8b949e;
  --color-prettylights-syntax-sublimelinter-gutter-mark: #484f58;
  --color-prettylights-syntax-constant-other-reference-link: #a5d6ff;
  --color-fg-default: #c9d1d9;
  --color-fg-muted: #8b949e;
  --color-fg-subtle: #484f58;
  --color-canvas-default: #0d1117;
  --color-canvas-subtle: #161b22;
  --color-border-default: #30363d;
  --color-border-muted: #21262d;
  --color-neutral-muted: #6e768166;
  --color-accent-fg: #58a6ff;
  --color-accent-emphasis: #1f6feb;
  --color-attention-subtle: #bb800926;
  --color-danger-fg: #f85149;
}

[data-color-mode*="light"] .wmde-markdown, [data-color-mode*="light"] .wmde-markdown-var, .wmde-markdown-var[data-color-mode*="light"], .wmde-markdown[data-color-mode*="light"], body[data-color-mode*="light"] {
  color-scheme: light;
  --color-prettylights-syntax-comment: #6e7781;
  --color-prettylights-syntax-constant: #0550ae;
  --color-prettylights-syntax-entity: #8250df;
  --color-prettylights-syntax-storage-modifier-import: #24292f;
  --color-prettylights-syntax-entity-tag: #116329;
  --color-prettylights-syntax-keyword: #cf222e;
  --color-prettylights-syntax-string: #0a3069;
  --color-prettylights-syntax-variable: #953800;
  --color-prettylights-syntax-brackethighlighter-unmatched: #82071e;
  --color-prettylights-syntax-invalid-illegal-text: #f6f8fa;
  --color-prettylights-syntax-invalid-illegal-bg: #82071e;
  --color-prettylights-syntax-carriage-return-text: #f6f8fa;
  --color-prettylights-syntax-carriage-return-bg: #cf222e;
  --color-prettylights-syntax-string-regexp: #116329;
  --color-prettylights-syntax-markup-list: #3b2300;
  --color-prettylights-syntax-markup-heading: #0550ae;
  --color-prettylights-syntax-markup-italic: #24292f;
  --color-prettylights-syntax-markup-bold: #24292f;
  --color-prettylights-syntax-markup-deleted-text: #82071e;
  --color-prettylights-syntax-markup-deleted-bg: #ffebe9;
  --color-prettylights-syntax-markup-inserted-text: #116329;
  --color-prettylights-syntax-markup-inserted-bg: #dafbe1;
  --color-prettylights-syntax-markup-changed-text: #953800;
  --color-prettylights-syntax-markup-changed-bg: #ffd8b5;
  --color-prettylights-syntax-markup-ignored-text: #eaeef2;
  --color-prettylights-syntax-markup-ignored-bg: #0550ae;
  --color-prettylights-syntax-meta-diff-range: #8250df;
  --color-prettylights-syntax-brackethighlighter-angle: #57606a;
  --color-prettylights-syntax-sublimelinter-gutter-mark: #8c959f;
  --color-prettylights-syntax-constant-other-reference-link: #0a3069;
  --color-fg-default: #24292f;
  --color-fg-muted: #57606a;
  --color-fg-subtle: #6e7781;
  --color-canvas-default: #fff;
  --color-canvas-subtle: #f6f8fa;
  --color-border-default: #d0d7de;
  --color-border-muted: #d8dee4;
  --color-neutral-muted: #afb8c133;
  --color-accent-fg: #0969da;
  --color-accent-emphasis: #0969da;
  --color-attention-subtle: #fff8c5;
  --color-danger-fg: #cf222e;
}

.wmde-markdown {
  -webkit-text-size-adjust: 100%;
  word-wrap: break-word;
  color: var(--color-fg-default);
  background-color: var(--color-canvas-default);
  font-family: -apple-system, BlinkMacSystemFont, Segoe UI, Noto Sans, Helvetica, Arial, sans-serif, Apple Color Emoji, Segoe UI Emoji;
  font-size: 16px;
  line-height: 1.5;
}

.wmde-markdown details, .wmde-markdown figcaption, .wmde-markdown figure {
  display: block;
}

.wmde-markdown summary {
  display: list-item;
}

.wmde-markdown [hidden] {
  display: none !important;
}

.wmde-markdown a {
  color: var(--color-accent-fg);
  background-color: #0000;
  text-decoration: none;
}

.wmde-markdown a:active, .wmde-markdown a:hover {
  outline-width: 0;
}

.wmde-markdown abbr[title] {
  border-bottom: none;
  text-decoration: underline dotted;
}

.wmde-markdown b, .wmde-markdown strong {
  font-weight: 600;
}

.wmde-markdown dfn {
  font-style: italic;
}

.wmde-markdown h1 {
  border-bottom: 1px solid var(--color-border-muted);
  margin: .67em 0;
  padding-bottom: .3em;
  font-size: 2em;
  font-weight: 600;
}

.wmde-markdown mark {
  background-color: var(--color-attention-subtle);
  color: var(--color-text-primary);
}

.wmde-markdown small {
  font-size: 90%;
}

.wmde-markdown sub, .wmde-markdown sup {
  vertical-align: baseline;
  font-size: 75%;
  line-height: 0;
  position: relative;
}

.wmde-markdown sub {
  bottom: -.25em;
}

.wmde-markdown sup {
  top: -.5em;
}

.wmde-markdown img {
  box-sizing: content-box;
  background-color: var(--color-canvas-default);
  border-style: none;
  max-width: 100%;
  display: inline-block;
}

.wmde-markdown code, .wmde-markdown kbd, .wmde-markdown pre, .wmde-markdown samp {
  font-family: monospace;
  font-size: 1em;
}

.wmde-markdown figure {
  margin: 1em 40px;
}

.wmde-markdown hr {
  box-sizing: content-box;
  border: 0;
  border-bottom: 1px solid var(--color-border-muted);
  background: none;
  background-color: var(--color-border-default);
  height: .25em;
  margin: 24px 0;
  padding: 0;
  overflow: hidden;
}

.wmde-markdown input {
  font: inherit;
  font-family: inherit;
  font-size: inherit;
  line-height: inherit;
  margin: 0;
  overflow: visible;
}

.wmde-markdown [type="button"], .wmde-markdown [type="reset"], .wmde-markdown [type="submit"] {
  -webkit-appearance: button;
}

.wmde-markdown [type="button"]::-moz-focus-inner, .wmde-markdown [type="reset"]::-moz-focus-inner, .wmde-markdown [type="submit"]::-moz-focus-inner {
  border-style: none;
  padding: 0;
}

.wmde-markdown [type="button"]:-moz-focusring, .wmde-markdown [type="reset"]:-moz-focusring, .wmde-markdown [type="submit"]:-moz-focusring {
  outline: 1px dotted buttontext;
}

.wmde-markdown [type="checkbox"], .wmde-markdown [type="radio"] {
  box-sizing: border-box;
  padding: 0;
}

.wmde-markdown [type="number"]::-webkit-inner-spin-button, .wmde-markdown [type="number"]::-webkit-outer-spin-button {
  height: auto;
}

.wmde-markdown [type="search"] {
  -webkit-appearance: textfield;
  outline-offset: -2px;
}

.wmde-markdown [type="search"]::-webkit-search-cancel-button, .wmde-markdown [type="search"]::-webkit-search-decoration {
  -webkit-appearance: none;
}

.wmde-markdown ::-webkit-input-placeholder {
  color: inherit;
  opacity: .54;
}

.wmde-markdown ::-webkit-file-upload-button {
  -webkit-appearance: button;
  font: inherit;
}

.wmde-markdown a:hover {
  text-decoration: underline;
}

.wmde-markdown hr:before {
  content: "";
  display: table;
}

.wmde-markdown hr:after {
  clear: both;
  content: "";
  display: table;
}

.wmde-markdown table {
  border-spacing: 0;
  border-collapse: collapse;
  width: max-content;
  max-width: 100%;
  display: block;
}

.wmde-markdown td, .wmde-markdown th {
  padding: 0;
}

.wmde-markdown details summary {
  cursor: pointer;
}

.wmde-markdown details:not([open]) > :not(summary) {
  display: none !important;
}

.wmde-markdown kbd {
  color: var(--color-fg-default);
  vertical-align: middle;
  background-color: var(--color-canvas-subtle);
  border: solid 1px var(--color-neutral-muted);
  border-bottom-color: var(--color-neutral-muted);
  box-shadow: inset 0 -1px 0 var(--color-neutral-muted);
  border-radius: 6px;
  padding: 3px 5px;
  font: 11px / 10px ui-monospace, SFMono-Regular, SF Mono, Menlo, Consolas, Liberation Mono, monospace;
  display: inline-block;
}

.wmde-markdown h1, .wmde-markdown h2, .wmde-markdown h3, .wmde-markdown h4, .wmde-markdown h5, .wmde-markdown h6 {
  margin-top: 24px;
  margin-bottom: 16px;
  font-weight: 600;
  line-height: 1.25;
}

.wmde-markdown h2 {
  border-bottom: 1px solid var(--color-border-muted);
  padding-bottom: .3em;
  font-size: 1.5em;
  font-weight: 600;
}

.wmde-markdown h3 {
  font-size: 1.25em;
  font-weight: 600;
}

.wmde-markdown h4 {
  font-size: 1em;
  font-weight: 600;
}

.wmde-markdown h5 {
  font-size: .875em;
  font-weight: 600;
}

.wmde-markdown h6 {
  color: var(--color-fg-muted);
  font-size: .85em;
  font-weight: 600;
}

.wmde-markdown p {
  margin-top: 0;
  margin-bottom: 10px;
}

.wmde-markdown blockquote {
  color: var(--color-fg-muted);
  border-left: .25em solid var(--color-border-default);
  margin: 0;
  padding: 0 1em;
}

.wmde-markdown ul, .wmde-markdown ol {
  margin-top: 0;
  margin-bottom: 0;
  padding-left: 2em;
}

.wmde-markdown ol ol, .wmde-markdown ul ol {
  list-style-type: lower-roman;
}

.wmde-markdown ul ul ol, .wmde-markdown ul ol ol, .wmde-markdown ol ul ol, .wmde-markdown ol ol ol {
  list-style-type: lower-alpha;
}

.wmde-markdown dd {
  margin-left: 0;
}

.wmde-markdown tt, .wmde-markdown code {
  font-family: ui-monospace, SFMono-Regular, SF Mono, Menlo, Consolas, Liberation Mono, monospace;
  font-size: 12px;
}

.wmde-markdown pre {
  word-wrap: normal;
  margin-top: 0;
  margin-bottom: 0;
  font-family: ui-monospace, SFMono-Regular, SF Mono, Menlo, Consolas, Liberation Mono, monospace;
  font-size: 12px;
}

.wmde-markdown .octicon {
  vertical-align: text-bottom;
  fill: currentColor;
  display: inline-block;
  overflow: visible !important;
}

.wmde-markdown ::placeholder {
  color: var(--color-fg-subtle);
  opacity: 1;
}

.wmde-markdown input::-webkit-outer-spin-button, .wmde-markdown input::-webkit-inner-spin-button {
  -webkit-appearance: none;
  appearance: none;
  margin: 0;
}

.wmde-markdown [data-catalyst] {
  display: block;
}

.wmde-markdown:before {
  content: "";
  display: table;
}

.wmde-markdown:after {
  clear: both;
  content: "";
  display: table;
}

.wmde-markdown > :first-child {
  margin-top: 0 !important;
}

.wmde-markdown > :last-child {
  margin-bottom: 0 !important;
}

.wmde-markdown a:not([href]) {
  color: inherit;
  text-decoration: none;
}

.wmde-markdown .absent {
  color: var(--color-danger-fg);
}

.wmde-markdown a.anchor {
  float: left;
  margin-left: -20px;
  padding-right: 4px;
  line-height: 1;
}

.wmde-markdown .anchor:focus {
  outline: none;
}

.wmde-markdown p, .wmde-markdown blockquote, .wmde-markdown ul, .wmde-markdown ol, .wmde-markdown dl, .wmde-markdown table, .wmde-markdown pre, .wmde-markdown details {
  margin-top: 0;
  margin-bottom: 16px;
}

.wmde-markdown blockquote > :first-child {
  margin-top: 0;
}

.wmde-markdown blockquote > :last-child {
  margin-bottom: 0;
}

.wmde-markdown sup > a:before {
  content: "[";
}

.wmde-markdown sup > a:after {
  content: "]";
}

.wmde-markdown h1 .octicon-link, .wmde-markdown h2 .octicon-link, .wmde-markdown h3 .octicon-link, .wmde-markdown h4 .octicon-link, .wmde-markdown h5 .octicon-link, .wmde-markdown h6 .octicon-link {
  color: var(--color-fg-default);
  vertical-align: middle;
  visibility: hidden;
}

.wmde-markdown h1:hover .anchor, .wmde-markdown h2:hover .anchor, .wmde-markdown h3:hover .anchor, .wmde-markdown h4:hover .anchor, .wmde-markdown h5:hover .anchor, .wmde-markdown h6:hover .anchor {
  text-decoration: none;
}

.wmde-markdown h1:hover .anchor .octicon-link, .wmde-markdown h2:hover .anchor .octicon-link, .wmde-markdown h3:hover .anchor .octicon-link, .wmde-markdown h4:hover .anchor .octicon-link, .wmde-markdown h5:hover .anchor .octicon-link, .wmde-markdown h6:hover .anchor .octicon-link {
  visibility: visible;
}

.wmde-markdown h1 tt, .wmde-markdown h1 code, .wmde-markdown h2 tt, .wmde-markdown h2 code, .wmde-markdown h3 tt, .wmde-markdown h3 code, .wmde-markdown h4 tt, .wmde-markdown h4 code, .wmde-markdown h5 tt, .wmde-markdown h5 code, .wmde-markdown h6 tt, .wmde-markdown h6 code {
  font-size: inherit;
  padding: 0 .2em;
}

.wmde-markdown ul.no-list, .wmde-markdown ol.no-list {
  padding: 0;
  list-style-type: none;
}

.wmde-markdown ol[type="1"] {
  list-style-type: decimal;
}

.wmde-markdown ol[type="a"] {
  list-style-type: lower-alpha;
}

.wmde-markdown ol[type="i"] {
  list-style-type: lower-roman;
}

.wmde-markdown div > ol:not([type]) {
  list-style-type: decimal;
}

.wmde-markdown ul ul, .wmde-markdown ul ol, .wmde-markdown ol ol, .wmde-markdown ol ul {
  margin-top: 0;
  margin-bottom: 0;
}

.wmde-markdown li > p {
  margin-top: 16px;
}

.wmde-markdown li + li {
  margin-top: .25em;
}

.wmde-markdown dl {
  padding: 0;
}

.wmde-markdown dl dt {
  margin-top: 16px;
  padding: 0;
  font-size: 1em;
  font-style: italic;
  font-weight: 600;
}

.wmde-markdown dl dd {
  margin-bottom: 16px;
  padding: 0 16px;
}

.wmde-markdown table th {
  font-weight: 600;
}

.wmde-markdown table th, .wmde-markdown table td {
  border: 1px solid var(--color-border-default);
  padding: 6px 13px;
}

.wmde-markdown table tr {
  background-color: var(--color-canvas-default);
  border-top: 1px solid var(--color-border-muted);
}

.wmde-markdown table tr:nth-child(2n) {
  background-color: var(--color-canvas-subtle);
}

.wmde-markdown table img {
  background-color: #0000;
}

.wmde-markdown img[align="right"] {
  padding-left: 20px;
}

.wmde-markdown img[align="left"] {
  padding-right: 20px;
}

.wmde-markdown .emoji {
  vertical-align: text-top;
  background-color: #0000;
  max-width: none;
}

.wmde-markdown span.frame {
  display: block;
  overflow: hidden;
}

.wmde-markdown span.frame > span {
  float: left;
  border: 1px solid var(--color-border-default);
  width: auto;
  margin: 13px 0 0;
  padding: 7px;
  display: block;
  overflow: hidden;
}

.wmde-markdown span.frame span img {
  float: left;
  display: block;
}

.wmde-markdown span.frame span span {
  clear: both;
  color: var(--color-fg-default);
  padding: 5px 0 0;
  display: block;
}

.wmde-markdown span.align-center {
  clear: both;
  display: block;
  overflow: hidden;
}

.wmde-markdown span.align-center > span {
  text-align: center;
  margin: 13px auto 0;
  display: block;
  overflow: hidden;
}

.wmde-markdown span.align-center span img {
  text-align: center;
  margin: 0 auto;
}

.wmde-markdown span.align-right {
  clear: both;
  display: block;
  overflow: hidden;
}

.wmde-markdown span.align-right > span {
  text-align: right;
  margin: 13px 0 0;
  display: block;
  overflow: hidden;
}

.wmde-markdown span.align-right span img {
  text-align: right;
  margin: 0;
}

.wmde-markdown span.float-left {
  float: left;
  margin-right: 13px;
  display: block;
  overflow: hidden;
}

.wmde-markdown span.float-left span {
  margin: 13px 0 0;
}

.wmde-markdown span.float-right {
  float: right;
  margin-left: 13px;
  display: block;
  overflow: hidden;
}

.wmde-markdown span.float-right > span {
  text-align: right;
  margin: 13px auto 0;
  display: block;
  overflow: hidden;
}

.wmde-markdown code, .wmde-markdown tt {
  background-color: var(--color-neutral-muted);
  border-radius: 6px;
  margin: 0;
  padding: .2em .4em;
  font-size: 85%;
}

.wmde-markdown code br, .wmde-markdown tt br {
  display: none;
}

.wmde-markdown del code {
  text-decoration: inherit;
}

.wmde-markdown pre code {
  font-size: 100%;
}

.wmde-markdown pre > code {
  word-break: normal;
  white-space: pre;
  background: none;
  border: 0;
  margin: 0;
  padding: 0;
}

.wmde-markdown pre {
  background-color: var(--color-canvas-subtle);
  border-radius: 6px;
  font-size: 85%;
  line-height: 1.45;
}

.wmde-markdown pre code, .wmde-markdown pre tt {
  max-width: auto;
  line-height: inherit;
  word-wrap: normal;
  background-color: #0000;
  border: 0;
  margin: 0;
  padding: 0;
  display: inline;
  overflow: visible;
}

.wmde-markdown pre > code {
  padding: 16px;
  display: block;
  overflow: auto;
}

.wmde-markdown pre > code::-webkit-scrollbar {
  background: none;
  width: 8px;
  height: 8px;
}

.wmde-markdown pre > code::-webkit-scrollbar-thumb {
  background: var(--color-fg-muted);
  border-radius: 10px;
}

.wmde-markdown .csv-data td, .wmde-markdown .csv-data th {
  text-align: left;
  white-space: nowrap;
  padding: 5px;
  font-size: 12px;
  line-height: 1;
  overflow: hidden;
}

.wmde-markdown .csv-data .blob-num {
  text-align: right;
  background: var(--color-canvas-default);
  border: 0;
  padding: 10px 8px 9px;
}

.wmde-markdown .csv-data tr {
  border-top: 0;
}

.wmde-markdown .csv-data th {
  background: var(--color-canvas-subtle);
  border-top: 0;
  font-weight: 600;
}

.wmde-markdown .footnotes {
  color: var(--color-fg-muted);
  border-top: 1px solid var(--color-border-default);
  font-size: 12px;
}

.wmde-markdown .footnotes ol {
  padding-left: 16px;
}

.wmde-markdown .footnotes li {
  position: relative;
}

.wmde-markdown .footnotes li:target:before {
  pointer-events: none;
  content: "";
  border: 2px solid var(--color-accent-emphasis);
  border-radius: 6px;
  position: absolute;
  inset: -8px -8px -8px -24px;
}

.wmde-markdown .footnotes li:target {
  color: var(--color-fg-default);
}

.wmde-markdown .footnotes .data-footnote-backref g-emoji {
  font-family: monospace;
}

.wmde-markdown .task-list-item {
  list-style-type: none;
}

.wmde-markdown .task-list-item label {
  font-weight: 400;
}

.wmde-markdown .task-list-item.enabled label {
  cursor: pointer;
}

.wmde-markdown .task-list-item + .wmde-markdown .task-list-item {
  margin-top: 3px;
}

.wmde-markdown .task-list-item .handle {
  display: none;
}

.wmde-markdown .task-list-item-checkbox, .wmde-markdown .contains-task-list input[type="checkbox"] {
  vertical-align: middle;
  margin: 0 .2em .25em -1.6em;
}

.wmde-markdown .contains-task-list:dir(rtl) .task-list-item-checkbox, .wmde-markdown .contains-task-list:dir(rtl) input[type="checkbox"] {
  margin: 0 -1.6em .25em .2em;
}

.wmde-markdown ::-webkit-calendar-picker-indicator {
  filter: invert(50%);
}

.wmde-markdown pre {
  position: relative;
}

.wmde-markdown pre .copied {
  visibility: hidden;
  cursor: pointer;
  color: var(--color-fg-default);
  background: var(--color-border-default);
  border-radius: 5px;
  padding: 6px;
  font-size: 12px;
  transition: all .3s;
  display: flex;
  position: absolute;
  top: 6px;
  right: 6px;
}

.wmde-markdown pre .copied .octicon-copy {
  display: block;
}

.wmde-markdown pre .copied .octicon-check {
  display: none;
}

.wmde-markdown pre:hover .copied {
  visibility: visible;
}

.wmde-markdown pre:hover .copied:hover {
  background: var(--color-prettylights-syntax-entity-tag);
  color: var(--color-canvas-default);
}

.wmde-markdown pre:hover .copied:active, .wmde-markdown pre .copied.active {
  background: var(--color-copied-active-bg);
  color: var(--color-canvas-default);
}

.wmde-markdown pre .active .octicon-copy {
  display: none;
}

.wmde-markdown pre .active .octicon-check {
  display: block;
}

.wmde-markdown .markdown-alert {
  color: inherit;
  border-left: .25em solid var(--borderColor-default, var(--color-border-default));
  margin-bottom: 16px;
  padding: .5rem 1em;
}

.wmde-markdown .markdown-alert > :last-child {
  margin-bottom: 0 !important;
}

.wmde-markdown .markdown-alert .markdown-alert-title {
  align-items: center;
  font-size: 14px;
  font-weight: 500;
  line-height: 1;
  display: flex;
}

.wmde-markdown .markdown-alert .markdown-alert-title svg.octicon {
  margin-right: var(--base-size-8, 8px) !important;
}

.wmde-markdown .markdown-alert.markdown-alert-note {
  border-left-color: var(--borderColor-accent-emphasis, var(--color-accent-emphasis));
}

.wmde-markdown .markdown-alert.markdown-alert-note .markdown-alert-title {
  color: var(--fgColor-accent, var(--color-accent-fg));
}

.wmde-markdown .markdown-alert.markdown-alert-tip {
  border-left-color: var(--borderColor-success-emphasis, var(--color-success-emphasis));
}

.wmde-markdown .markdown-alert.markdown-alert-tip .markdown-alert-title {
  color: var(--fgColor-success, var(--color-success-fg));
}

.wmde-markdown .markdown-alert.markdown-alert-important {
  border-left-color: var(--borderColor-done-emphasis, var(--color-done-emphasis));
}

.wmde-markdown .markdown-alert.markdown-alert-important .markdown-alert-title {
  color: var(--fgColor-done, var(--color-done-fg));
}

.wmde-markdown .markdown-alert.markdown-alert-warning {
  border-left-color: var(--borderColor-attention-emphasis, var(--color-attention-emphasis));
}

.wmde-markdown .markdown-alert.markdown-alert-warning .markdown-alert-title {
  color: var(--fgColor-attention, var(--color-attention-fg));
}

.wmde-markdown .markdown-alert.markdown-alert-caution {
  border-left-color: var(--borderColor-danger-emphasis, var(--color-danger-emphasis));
}

.wmde-markdown .markdown-alert.markdown-alert-caution .markdown-alert-title {
  color: var(--fgColor-danger, var(--color-danger-fg));
}

.wmde-markdown .highlight-line {
  background-color: var(--color-neutral-muted);
}

.wmde-markdown .code-line.line-number:before {
  text-align: right;
  width: 1rem;
  color: var(--color-fg-subtle);
  content: attr(line);
  white-space: nowrap;
  margin-right: 16px;
  display: inline-block;
}

.wmde-markdown .token.comment, .wmde-markdown .token.prolog, .wmde-markdown .token.doctype, .wmde-markdown .token.cdata {
  color: var(--color-prettylights-syntax-comment);
}

.wmde-markdown .token.namespace {
  opacity: .7;
}

.wmde-markdown .token.property, .wmde-markdown .token.tag, .wmde-markdown .token.selector, .wmde-markdown .token.constant, .wmde-markdown .token.symbol, .wmde-markdown .token.deleted {
  color: var(--color-prettylights-syntax-entity-tag);
}

.wmde-markdown .token.maybe-class-name {
  color: var(--color-prettylights-syntax-variable);
}

.wmde-markdown .token.property-access, .wmde-markdown .token.operator, .wmde-markdown .token.boolean, .wmde-markdown .token.number, .wmde-markdown .token.selector .token.class, .wmde-markdown .token.attr-name, .wmde-markdown .token.string, .wmde-markdown .token.char, .wmde-markdown .token.builtin {
  color: var(--color-prettylights-syntax-constant);
}

.wmde-markdown .token.deleted {
  color: var(--color-prettylights-syntax-markup-deleted-text);
}

.wmde-markdown .code-line .token.deleted {
  background-color: var(--color-prettylights-syntax-markup-deleted-bg);
}

.wmde-markdown .token.inserted {
  color: var(--color-prettylights-syntax-markup-inserted-text);
}

.wmde-markdown .code-line .token.inserted {
  background-color: var(--color-prettylights-syntax-markup-inserted-bg);
}

.wmde-markdown .token.variable {
  color: var(--color-prettylights-syntax-constant);
}

.wmde-markdown .token.entity, .wmde-markdown .token.url, .wmde-markdown .language-css .token.string, .wmde-markdown .style .token.string, .wmde-markdown .token.color, .wmde-markdown .token.atrule, .wmde-markdown .token.attr-value, .wmde-markdown .token.function, .wmde-markdown .token.class-name {
  color: var(--color-prettylights-syntax-string);
}

.wmde-markdown .token.rule, .wmde-markdown .token.regex, .wmde-markdown .token.important, .wmde-markdown .token.keyword {
  color: var(--color-prettylights-syntax-keyword);
}

.wmde-markdown .token.coord {
  color: var(--color-prettylights-syntax-meta-diff-range);
}

.wmde-markdown .token.important, .wmde-markdown .token.bold {
  font-weight: bold;
}

.wmde-markdown .token.italic {
  font-style: italic;
}

.wmde-markdown .token.entity {
  cursor: help;
}

:root {
  --background: #fff;
  --foreground: #171717;
  --card: #fff;
  --card-foreground: #171717;
  --popover: #fff;
  --popover-foreground: #171717;
  --primary: #171717;
  --primary-foreground: #fafafa;
  --secondary: #f5f5f5;
  --secondary-foreground: #171717;
  --muted: #f5f5f5;
  --muted-foreground: #737373;
  --accent: #f5f5f5;
  --accent-foreground: #171717;
  --destructive: #ef4444;
  --destructive-foreground: #fafafa;
  --border: #e5e5e5;
  --input: #e5e5e5;
  --ring: #171717;
  --radius: .5rem;
}

.dark {
  --background: #0a0a0a;
  --foreground: #ededed;
  --card: #0a0a0a;
  --card-foreground: #ededed;
  --popover: #0a0a0a;
  --popover-foreground: #ededed;
  --primary: #ededed;
  --primary-foreground: #171717;
  --secondary: #262626;
  --secondary-foreground: #ededed;
  --muted: #262626;
  --muted-foreground: #a3a3a3;
  --accent: #262626;
  --accent-foreground: #ededed;
  --destructive: #dc2626;
  --destructive-foreground: #ededed;
  --border: #262626;
  --input: #262626;
  --ring: #d4d4d8;
}

* {
  border-color: var(--border);
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: var(--font-sans), Arial, Helvetica, sans-serif;
  transition: background-color .3s, color .3s;
}

.cherry-editor-wrapper {
  width: 100%;
}

.cherry-editor .cherry {
  border-style: var(--tw-border-style);
  background-color: var(--background);
  color: var(--foreground);
  border-width: 0;
}

.cherry-editor .cherry-toolbar {
  border-bottom-style: var(--tw-border-style);
  border-bottom-width: 1px;
  border-color: var(--border);
  background-color: var(--card);
}

.cherry-editor .cherry-toolbar .toolbar-item {
  color: var(--foreground);
}

@media (hover: hover) {
  .cherry-editor .cherry-toolbar .toolbar-item:hover {
    background-color: var(--muted);
  }
}

.cherry-editor .cherry-editor, .cherry-editor .cherry-previewer {
  background-color: var(--background);
  color: var(--foreground);
}

.cherry-editor .cherry-previewer .cherry-markdown {
  color: var(--foreground);
}

.dark .cherry-editor .cherry {
  background-color: var(--background);
  color: var(--foreground);
}

.dark .cherry-editor .cherry-toolbar {
  border-color: var(--border);
  background-color: var(--card);
}

.dark .cherry-editor .cherry-toolbar .toolbar-item {
  color: var(--foreground);
}

.dark .cherry-editor .cherry-editor, .dark .cherry-editor .cherry-previewer {
  background-color: var(--background);
  color: var(--foreground);
}

.markdown-renderer .prose, .markdown-renderer .prose h1, .markdown-renderer .prose h2, .markdown-renderer .prose h3, .markdown-renderer .prose h4, .markdown-renderer .prose h5, .markdown-renderer .prose h6 {
  color: var(--foreground);
}

.markdown-renderer .prose a {
  color: var(--primary);
}

@media (hover: hover) {
  .markdown-renderer .prose a:hover {
    color: var(--primary);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .markdown-renderer .prose a:hover {
      color: color-mix(in oklab, var(--primary) 80%, transparent);
    }
  }
}

.markdown-renderer .prose code {
  border-radius: var(--radius);
  background-color: var(--muted);
  padding-inline: calc(var(--spacing) * 1);
  padding-block: calc(var(--spacing) * .5);
  color: var(--foreground);
}

.markdown-renderer .prose pre {
  border-style: var(--tw-border-style);
  border-width: 1px;
  border-color: var(--border);
  background-color: var(--muted);
}

.markdown-renderer .prose blockquote {
  border-left-color: var(--primary);
  color: var(--muted-foreground);
}

.markdown-renderer .prose table, .markdown-renderer .prose th, .markdown-renderer .prose td {
  border-color: var(--border);
}

.markdown-renderer .prose th {
  background-color: var(--muted);
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }

  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.animate-fade-in {
  animation: .6s ease-out fadeIn;
}

.animate-slide-in {
  animation: .5s ease-out slideIn;
}

.dark .hover-lift:hover {
  box-shadow: 0 8px 25px #0000004d;
}

.btn-primary {
  transition: all .3s;
  position: relative;
  overflow: hidden;
}

.btn-primary:before {
  content: "";
  background: linear-gradient(90deg, #0000, #fff3, #0000);
  width: 100%;
  height: 100%;
  transition: left .5s;
  position: absolute;
  top: 0;
  left: -100%;
}

.btn-primary:hover:before {
  left: 100%;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.animate-spin {
  animation: 1s linear infinite spin;
}

@keyframes pulse {
  50% {
    opacity: .5;
  }
}

.animate-pulse {
  animation: 2s cubic-bezier(.4, 0, .6, 1) infinite pulse;
}

@media (width <= 768px) {
  .container {
    padding: 0 1rem;
  }

  .text-responsive {
    font-size: .875rem;
  }

  .grid-responsive {
    grid-template-columns: 1fr;
  }
}

@media (width <= 640px) {
  .hide-mobile {
    display: none;
  }

  .text-responsive {
    font-size: .75rem;
  }
}

.line-clamp-1 {
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  display: -webkit-box;
  overflow: hidden;
}

.line-clamp-2 {
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  display: -webkit-box;
  overflow: hidden;
}

.line-clamp-3 {
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  display: -webkit-box;
  overflow: hidden;
}

@keyframes bounce-in {
  0% {
    opacity: 0;
    transform: scale(.3);
  }

  50% {
    transform: scale(1.05);
  }

  70% {
    transform: scale(.9);
  }

  100% {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes slide-up {
  from {
    opacity: 0;
    transform: translateY(20px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes glow {
  0%, 100% {
    box-shadow: 0 0 5px rgba(var(--primary), .5);
  }

  50% {
    box-shadow: 0 0 20px rgba(var(--primary), .8);
  }
}

.animate-bounce-in {
  animation: .6s ease-out bounce-in;
}

.animate-slide-up {
  animation: .4s ease-out slide-up;
}

.animate-glow {
  animation: 2s ease-in-out infinite glow;
}

.hover-lift {
  transition: all .3s cubic-bezier(.4, 0, .2, 1);
}

.hover-lift:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px -5px #0000001a, 0 10px 10px -5px #0000000a;
}

.hover-scale {
  transition: transform .2s ease-in-out;
}

.hover-scale:hover {
  transform: scale(1.05);
}

.gradient-text {
  background: linear-gradient(135deg, hsl(var(--primary)), hsl(var(--secondary)));
  -webkit-text-fill-color: transparent;
  -webkit-background-clip: text;
  background-clip: text;
}

.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: hsl(var(--muted));
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: hsl(var(--primary));
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: hsl(var(--primary) / .8);
}

@property --tw-translate-x {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}

@property --tw-translate-y {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}

@property --tw-translate-z {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}

@property --tw-space-y-reverse {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}

@property --tw-space-x-reverse {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}

@property --tw-divide-y-reverse {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}

@property --tw-border-style {
  syntax: "*";
  inherits: false;
  initial-value: solid;
}

@property --tw-gradient-position {
  syntax: "*";
  inherits: false
}

@property --tw-gradient-from {
  syntax: "<color>";
  inherits: false;
  initial-value: #0000;
}

@property --tw-gradient-via {
  syntax: "<color>";
  inherits: false;
  initial-value: #0000;
}

@property --tw-gradient-to {
  syntax: "<color>";
  inherits: false;
  initial-value: #0000;
}

@property --tw-gradient-stops {
  syntax: "*";
  inherits: false
}

@property --tw-gradient-via-stops {
  syntax: "*";
  inherits: false
}

@property --tw-gradient-from-position {
  syntax: "<length-percentage>";
  inherits: false;
  initial-value: 0%;
}

@property --tw-gradient-via-position {
  syntax: "<length-percentage>";
  inherits: false;
  initial-value: 50%;
}

@property --tw-gradient-to-position {
  syntax: "<length-percentage>";
  inherits: false;
  initial-value: 100%;
}

@property --tw-leading {
  syntax: "*";
  inherits: false
}

@property --tw-font-weight {
  syntax: "*";
  inherits: false
}

@property --tw-tracking {
  syntax: "*";
  inherits: false
}

@property --tw-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}

@property --tw-shadow-color {
  syntax: "*";
  inherits: false
}

@property --tw-shadow-alpha {
  syntax: "<percentage>";
  inherits: false;
  initial-value: 100%;
}

@property --tw-inset-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}

@property --tw-inset-shadow-color {
  syntax: "*";
  inherits: false
}

@property --tw-inset-shadow-alpha {
  syntax: "<percentage>";
  inherits: false;
  initial-value: 100%;
}

@property --tw-ring-color {
  syntax: "*";
  inherits: false
}

@property --tw-ring-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}

@property --tw-inset-ring-color {
  syntax: "*";
  inherits: false
}

@property --tw-inset-ring-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}

@property --tw-ring-inset {
  syntax: "*";
  inherits: false
}

@property --tw-ring-offset-width {
  syntax: "<length>";
  inherits: false;
  initial-value: 0;
}

@property --tw-ring-offset-color {
  syntax: "*";
  inherits: false;
  initial-value: #fff;
}

@property --tw-ring-offset-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}

@property --tw-backdrop-blur {
  syntax: "*";
  inherits: false
}

@property --tw-backdrop-brightness {
  syntax: "*";
  inherits: false
}

@property --tw-backdrop-contrast {
  syntax: "*";
  inherits: false
}

@property --tw-backdrop-grayscale {
  syntax: "*";
  inherits: false
}

@property --tw-backdrop-hue-rotate {
  syntax: "*";
  inherits: false
}

@property --tw-backdrop-invert {
  syntax: "*";
  inherits: false
}

@property --tw-backdrop-opacity {
  syntax: "*";
  inherits: false
}

@property --tw-backdrop-saturate {
  syntax: "*";
  inherits: false
}

@property --tw-backdrop-sepia {
  syntax: "*";
  inherits: false
}

@property --tw-duration {
  syntax: "*";
  inherits: false
}

@property --tw-scale-x {
  syntax: "*";
  inherits: false;
  initial-value: 1;
}

@property --tw-scale-y {
  syntax: "*";
  inherits: false;
  initial-value: 1;
}

@property --tw-scale-z {
  syntax: "*";
  inherits: false;
  initial-value: 1;
}

/*# sourceMappingURL=src_app_globals_css_f9ee138c._.single.css.map*/