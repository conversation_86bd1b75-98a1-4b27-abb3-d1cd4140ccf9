/* [project]/node_modules/@uiw/react-md-editor/esm/index.css [app-client] (css) */
.w-md-editor {
  text-align: left;
  color: var(--color-fg-default);
  --md-editor-font-family: "Helvetica Neue", Helvetica, Arial, sans-serif;
  --md-editor-background-color: var(--color-canvas-default, #fff);
  --md-editor-box-shadow-color: var(--color-border-default, #d0d7de);
  box-shadow: 0 0 0 1px var(--md-editor-box-shadow-color), 0 0 0 var(--md-editor-box-shadow-color), 0 1px 1px var(--md-editor-box-shadow-color);
  background-color: var(--md-editor-background-color);
  border-radius: 3px;
  flex-direction: column;
  padding-bottom: 1px;
  font-family: Helvetica Neue, Helvetica, Arial, sans-serif;
  display: flex;
  position: relative;
}

.w-md-editor.w-md-editor-rtl {
  text-align: right !important;
  direction: rtl !important;
}

.w-md-editor.w-md-editor-rtl .w-md-editor-preview {
  box-shadow: inset -1px 0 0 0 var(--md-editor-box-shadow-color);
  left: 0;
  right: unset !important;
  text-align: right !important;
}

.w-md-editor.w-md-editor-rtl .w-md-editor-text {
  text-align: right !important;
}

.w-md-editor-toolbar {
  height: fit-content;
}

.w-md-editor-content {
  border-radius: 0 0 3px;
  height: 100%;
  position: relative;
  overflow: auto;
}

.w-md-editor .copied {
  display: none !important;
}

.w-md-editor-input {
  width: 50%;
  height: 100%;
}

.w-md-editor-text-pre > code {
  word-break: break-word !important;
  white-space: pre-wrap !important;
}

.w-md-editor-preview {
  box-sizing: border-box;
  width: 50%;
  box-shadow: inset 1px 0 0 0 var(--md-editor-box-shadow-color);
  border-radius: 0 0 5px;
  flex-direction: column;
  padding: 10px 20px;
  display: flex;
  position: absolute;
  top: 0;
  bottom: 0;
  right: 0;
  overflow: auto;
}

.w-md-editor-preview .anchor {
  display: none;
}

.w-md-editor-preview .contains-task-list li.task-list-item {
  list-style: none;
}

.w-md-editor-show-preview .w-md-editor-input {
  background-color: var(--md-editor-background-color);
  width: 0%;
  overflow: hidden;
}

.w-md-editor-show-preview .w-md-editor-preview {
  width: 100%;
  box-shadow: inset 0 0;
}

.w-md-editor-show-edit .w-md-editor-input {
  width: 100%;
}

.w-md-editor-show-edit .w-md-editor-preview {
  width: 0%;
  padding: 0;
}

.w-md-editor-fullscreen {
  z-index: 99999;
  position: fixed;
  inset: 0;
  overflow: hidden;
  height: 100% !important;
}

.w-md-editor-fullscreen .w-md-editor-content {
  height: 100%;
}

/*# sourceMappingURL=node_modules_%40uiw_react-md-editor_esm_index_css_f9ee138c._.single.css.map*/