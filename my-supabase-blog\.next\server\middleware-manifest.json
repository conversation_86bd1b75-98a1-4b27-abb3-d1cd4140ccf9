{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_f58e8655._.js", "server/edge/chunks/[root-of-the-server]__c6c91fdf._.js", "server/edge/chunks/edge-wrapper_c6630286.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!_next\\/static|_next\\/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "wC6mVzmAcSTFXEAQa4K0/xSyQABPm2lxWxxuURttMKc=", "__NEXT_PREVIEW_MODE_ID": "af95e7974d8278a156651c38b8bb152d", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "a76af867b89b818a2b648479a2b5d7eec147579b74eb25f8666b69f33979d146", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "0cd00a981cb2910e032d28f5ec1267b47b26fd240f1bd85dd4f2d83ab0cbb097"}}}, "sortedMiddleware": ["/"], "functions": {}}