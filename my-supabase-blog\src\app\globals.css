@import "tailwindcss";

/* Markdown Editor Styles */
@import "@uiw/react-md-editor/markdown-editor.css";
@import "@uiw/react-markdown-preview/markdown.css";

:root {
  --background: #ffffff;
  --foreground: #171717;
  --card: #ffffff;
  --card-foreground: #171717;
  --popover: #ffffff;
  --popover-foreground: #171717;
  --primary: #171717;
  --primary-foreground: #fafafa;
  --secondary: #f5f5f5;
  --secondary-foreground: #171717;
  --muted: #f5f5f5;
  --muted-foreground: #737373;
  --accent: #f5f5f5;
  --accent-foreground: #171717;
  --destructive: #ef4444;
  --destructive-foreground: #fafafa;
  --border: #e5e5e5;
  --input: #e5e5e5;
  --ring: #171717;
  --radius: 0.5rem;
}

.dark {
  --background: #0a0a0a;
  --foreground: #ededed;
  --card: #0a0a0a;
  --card-foreground: #ededed;
  --popover: #0a0a0a;
  --popover-foreground: #ededed;
  --primary: #ededed;
  --primary-foreground: #171717;
  --secondary: #262626;
  --secondary-foreground: #ededed;
  --muted: #262626;
  --muted-foreground: #a3a3a3;
  --accent: #262626;
  --accent-foreground: #ededed;
  --destructive: #dc2626;
  --destructive-foreground: #ededed;
  --border: #262626;
  --input: #262626;
  --ring: #d4d4d8;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
  --radius: var(--radius);
}

* {
  border-color: var(--border);
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: var(--font-sans), Arial, Helvetica, sans-serif;
  transition: background-color 0.3s ease, color 0.3s ease;
}

/* Cherry Markdown 編輯器樣式 */
.cherry-editor-wrapper {
  @apply w-full;
}

.cherry-editor .cherry {
  @apply border-0 bg-background text-foreground;
}

.cherry-editor .cherry-toolbar {
  @apply bg-card border-b border-border;
}

.cherry-editor .cherry-toolbar .toolbar-item {
  @apply text-foreground hover:bg-muted;
}

.cherry-editor .cherry-editor {
  @apply bg-background text-foreground;
}

.cherry-editor .cherry-previewer {
  @apply bg-background text-foreground;
}

.cherry-editor .cherry-previewer .cherry-markdown {
  @apply text-foreground;
}

/* Cherry Markdown 深色模式 */
.dark .cherry-editor .cherry {
  @apply bg-background text-foreground;
}

.dark .cherry-editor .cherry-toolbar {
  @apply bg-card border-border;
}

.dark .cherry-editor .cherry-toolbar .toolbar-item {
  @apply text-foreground;
}

.dark .cherry-editor .cherry-editor {
  @apply bg-background text-foreground;
}

.dark .cherry-editor .cherry-previewer {
  @apply bg-background text-foreground;
}

/* Markdown 渲染器樣式 */
.markdown-renderer .prose {
  @apply text-foreground;
}

.markdown-renderer .prose h1,
.markdown-renderer .prose h2,
.markdown-renderer .prose h3,
.markdown-renderer .prose h4,
.markdown-renderer .prose h5,
.markdown-renderer .prose h6 {
  @apply text-foreground;
}

.markdown-renderer .prose a {
  @apply text-primary hover:text-primary/80;
}

.markdown-renderer .prose code {
  @apply bg-muted text-foreground px-1 py-0.5 rounded;
}

.markdown-renderer .prose pre {
  @apply bg-muted border border-border;
}

.markdown-renderer .prose blockquote {
  @apply border-l-primary text-muted-foreground;
}

.markdown-renderer .prose table {
  @apply border-border;
}

.markdown-renderer .prose th,
.markdown-renderer .prose td {
  @apply border-border;
}

.markdown-renderer .prose th {
  @apply bg-muted;
}

/* 動畫效果 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.animate-fade-in {
  animation: fadeIn 0.6s ease-out;
}

.animate-slide-in {
  animation: slideIn 0.5s ease-out;
}

/* 懸停效果 */
.hover-lift {
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.hover-lift:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.dark .hover-lift:hover {
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

/* 按鈕動畫 */
.btn-primary {
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
}

.btn-primary::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.btn-primary:hover::before {
  left: 100%;
}

/* 載入動畫 */
@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.animate-spin {
  animation: spin 1s linear infinite;
}

/* 脈衝動畫 */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* 響應式設計 */
@media (max-width: 768px) {
  .container {
    padding: 0 1rem;
  }

  .text-responsive {
    font-size: 0.875rem;
  }

  .grid-responsive {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 640px) {
  .hide-mobile {
    display: none;
  }

  .text-responsive {
    font-size: 0.75rem;
  }
}
