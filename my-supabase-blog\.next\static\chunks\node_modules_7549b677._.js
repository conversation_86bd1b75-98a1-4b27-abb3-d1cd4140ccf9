(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/node_modules/@uiw/react-md-editor/esm/index.js [app-client] (ecmascript, next/dynamic entry, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_refractor_lang_b98ac696._.js",
  "static/chunks/node_modules_refractor_lib_3179224d._.js",
  "static/chunks/node_modules_17e71297._.js",
  "static/chunks/node_modules_micromark-core-commonmark_dev_lib_36a4b45d._.js",
  "static/chunks/node_modules_31d2c696._.js",
  "static/chunks/node_modules_@uiw_react-md-editor_esm_d7c328d5._.js",
  "static/chunks/node_modules_e4e57ffc._.js",
  {
    "path": "static/chunks/node_modules_@uiw_e694d1d1._.css",
    "included": [
      "[project]/node_modules/@uiw/react-markdown-preview/esm/styles/markdown.css [app-client] (css)",
      "[project]/node_modules/@uiw/react-md-editor/esm/components/Toolbar/Child.css [app-client] (css)",
      "[project]/node_modules/@uiw/react-md-editor/esm/components/Toolbar/index.css [app-client] (css)",
      "[project]/node_modules/@uiw/react-md-editor/esm/components/TextArea/index.css [app-client] (css)",
      "[project]/node_modules/@uiw/react-md-editor/esm/components/DragBar/index.css [app-client] (css)",
      "[project]/node_modules/@uiw/react-md-editor/esm/index.css [app-client] (css)"
    ],
    "moduleChunks": [
      "static/chunks/node_modules_@uiw_react-markdown-preview_esm_styles_markdown_css_f9ee138c._.single.css",
      "static/chunks/node_modules_@uiw_react-md-editor_esm_components_Toolbar_Child_css_f9ee138c._.single.css",
      "static/chunks/node_modules_@uiw_react-md-editor_esm_components_Toolbar_index_css_f9ee138c._.single.css",
      "static/chunks/node_modules_@uiw_react-md-editor_esm_components_TextArea_index_css_f9ee138c._.single.css",
      "static/chunks/node_modules_@uiw_react-md-editor_esm_components_DragBar_index_css_f9ee138c._.single.css",
      "static/chunks/node_modules_@uiw_react-md-editor_esm_index_css_f9ee138c._.single.css"
    ]
  },
  "static/chunks/node_modules_@uiw_react-md-editor_esm_index_0fe66f64.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@uiw/react-md-editor/esm/index.js [app-client] (ecmascript, next/dynamic entry)");
    });
});
}}),
"[project]/node_modules/@supabase/node-fetch/browser.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@supabase/node-fetch/browser.js [app-client] (ecmascript)");
    });
});
}}),
}]);