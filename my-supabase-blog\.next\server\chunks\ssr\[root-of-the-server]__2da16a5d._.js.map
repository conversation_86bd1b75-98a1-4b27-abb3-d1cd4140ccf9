{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 104, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test2/my-supabase-blog/src/lib/supabase/server.ts"], "sourcesContent": ["import { createServerClient } from '@supabase/ssr'\nimport { cookies } from 'next/headers'\n\nexport async function createClient() {\n  const cookieStore = await cookies()\n\n  return createServerClient(\n    process.env.NEXT_PUBLIC_SUPABASE_URL!,\n    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,\n    {\n      cookies: {\n        getAll() {\n          return cookieStore.getAll()\n        },\n        setAll(cookiesToSet) {\n          try {\n            cookiesToSet.forEach(({ name, value, options }) =>\n              cookieStore.set(name, value, options)\n            )\n          } catch {\n            // The `setAll` method was called from a Server Component.\n            // This can be ignored if you have middleware refreshing\n            // user sessions.\n          }\n        },\n      },\n    }\n  )\n}\n"], "names": [], "mappings": ";;;AAAA;AAAA;AACA;;;AAEO,eAAe;IACpB,MAAM,cAAc,MAAM,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAEhC,OAAO,CAAA,GAAA,yKAAA,CAAA,qBAAkB,AAAD,sUAGtB;QACE,SAAS;YACP;gBACE,OAAO,YAAY,MAAM;YAC3B;YACA,QAAO,YAAY;gBACjB,IAAI;oBACF,aAAa,OAAO,CAAC,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,OAAO,EAAE,GAC5C,YAAY,GAAG,CAAC,MAAM,OAAO;gBAEjC,EAAE,OAAM;gBACN,0DAA0D;gBAC1D,wDAAwD;gBACxD,iBAAiB;gBACnB;YACF;QACF;IACF;AAEJ", "debugId": null}}, {"offset": {"line": 137, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test2/my-supabase-blog/src/lib/auth.ts"], "sourcesContent": ["import { createClient } from '@/lib/supabase/server'\n\nexport async function getCurrentUser() {\n  const supabase = await createClient()\n  const { data: { user } } = await supabase.auth.getUser()\n  return user\n}\n\nexport async function isAdmin(userId?: string) {\n  if (!userId) return false\n\n  const supabase = await createClient()\n\n  // 方法1: 檢查 profiles 表中的 is_admin 欄位\n  const { data: profile } = await supabase\n    .from('profiles')\n    .select('is_admin')\n    .eq('id', userId)\n    .single()\n\n  if (profile?.is_admin) return true\n\n  // 方法2: 檢查是否為預設管理員 email\n  const { data: { user } } = await supabase.auth.getUser()\n  const adminEmail = process.env.ADMIN_EMAIL\n\n  return user?.email === adminEmail\n}\n\nexport async function requireAdmin() {\n  const user = await getCurrentUser()\n  if (!user) {\n    throw new Error('未登入')\n  }\n\n  const isUserAdmin = await isAdmin(user.id)\n  if (!isUserAdmin) {\n    throw new Error('需要管理員權限')\n  }\n\n  return user\n}\n"], "names": [], "mappings": ";;;;;AAAA;;AAEO,eAAe;IACpB,MAAM,WAAW,MAAM,CAAA,GAAA,gIAAA,CAAA,eAAY,AAAD;IAClC,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;IACtD,OAAO;AACT;AAEO,eAAe,QAAQ,MAAe;IAC3C,IAAI,CAAC,QAAQ,OAAO;IAEpB,MAAM,WAAW,MAAM,CAAA,GAAA,gIAAA,CAAA,eAAY,AAAD;IAElC,mCAAmC;IACnC,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,MAAM,SAC7B,IAAI,CAAC,YACL,MAAM,CAAC,YACP,EAAE,CAAC,MAAM,QACT,MAAM;IAET,IAAI,SAAS,UAAU,OAAO;IAE9B,wBAAwB;IACxB,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;IACtD,MAAM,aAAa,QAAQ,GAAG,CAAC,WAAW;IAE1C,OAAO,MAAM,UAAU;AACzB;AAEO,eAAe;IACpB,MAAM,OAAO,MAAM;IACnB,IAAI,CAAC,MAAM;QACT,MAAM,IAAI,MAAM;IAClB;IAEA,MAAM,cAAc,MAAM,QAAQ,KAAK,EAAE;IACzC,IAAI,CAAC,aAAa;QAChB,MAAM,IAAI,MAAM;IAClB;IAEA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 177, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test2/my-supabase-blog/src/components/markdown-renderer.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const MarkdownRenderer = registerClientReference(\n    function() { throw new Error(\"Attempted to call MarkdownRenderer() from the server but <PERSON><PERSON><PERSON><PERSON><PERSON> is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/markdown-renderer.tsx <module evaluation>\",\n    \"MarkdownRenderer\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,mBAAmB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAClD;IAAa,MAAM,IAAI,MAAM;AAAgP,GAC7Q,sEACA", "debugId": null}}, {"offset": {"line": 191, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test2/my-supabase-blog/src/components/markdown-renderer.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const MarkdownRenderer = registerClientReference(\n    function() { throw new Error(\"Attempted to call MarkdownRenderer() from the server but <PERSON><PERSON><PERSON><PERSON><PERSON> is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/markdown-renderer.tsx\",\n    \"MarkdownRenderer\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,mBAAmB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAClD;IAAa,MAAM,IAAI,MAAM;AAAgP,GAC7Q,kDACA", "debugId": null}}, {"offset": {"line": 205, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 215, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test2/my-supabase-blog/src/components/theme-toggle.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const ThemeToggle = registerClientReference(\n    function() { throw new Error(\"Attempted to call ThemeToggle() from the server but ThemeToggle is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/theme-toggle.tsx <module evaluation>\",\n    \"ThemeToggle\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,cAAc,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC7C;IAAa,MAAM,IAAI,MAAM;AAAsO,GACnQ,iEACA", "debugId": null}}, {"offset": {"line": 229, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test2/my-supabase-blog/src/components/theme-toggle.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const ThemeToggle = registerClientReference(\n    function() { throw new Error(\"Attempted to call ThemeToggle() from the server but ThemeToggle is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/theme-toggle.tsx\",\n    \"ThemeToggle\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,cAAc,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC7C;IAAa,MAAM,IAAI,MAAM;AAAsO,GACnQ,6CACA", "debugId": null}}, {"offset": {"line": 243, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 253, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test2/my-supabase-blog/src/app/posts/%5Bid%5D/page.tsx"], "sourcesContent": ["import { notFound } from 'next/navigation'\nimport Link from 'next/link'\nimport { createClient } from '@/lib/supabase/server'\nimport { getCurrentUser, isAdmin } from '@/lib/auth'\nimport { Post } from '@/types/database'\nimport { MarkdownRenderer } from '@/components/markdown-renderer'\nimport { ThemeToggle } from '@/components/theme-toggle'\n\nasync function getPost(id: string): Promise<Post | null> {\n  const supabase = await createClient()\n  const { data: post, error } = await supabase\n    .from('posts')\n    .select('*')\n    .eq('id', id)\n    .single()\n\n  if (error) {\n    console.error('Error fetching post:', error)\n    return null\n  }\n\n  return post\n}\n\nexport default async function PostPage({ params }: { params: Promise<{ id: string }> }) {\n  const { id } = await params\n  const post = await getPost(id)\n  \n  if (!post) {\n    notFound()\n  }\n\n  const user = await getCurrentUser()\n  const userIsAdmin = user ? await isAdmin(user.id) : false\n\n  return (\n    <div className=\"min-h-screen bg-background\">\n      {/* Header */}\n      <header className=\"bg-card shadow-sm border-b border-border\">\n        <div className=\"max-w-4xl mx-auto px-4 py-6\">\n          <div className=\"flex justify-between items-center\">\n            <Link \n              href=\"/\"\n              className=\"text-2xl font-bold text-foreground hover:text-primary transition-colors\"\n            >\n              我的部落格\n            </Link>\n            <div className=\"flex items-center gap-4\">\n              <ThemeToggle />\n              {user ? (\n                <>\n                  <span className=\"text-sm text-muted-foreground\">\n                    歡迎, {user.email}\n                  </span>\n                  {userIsAdmin && (\n                    <>\n                      <Link\n                        href=\"/admin/new-post\"\n                        className=\"bg-primary text-primary-foreground px-4 py-2 rounded-md hover:bg-primary/90 transition-colors\"\n                      >\n                        新增文章\n                      </Link>\n                      <Link\n                        href=\"/admin/manage-posts\"\n                        className=\"bg-secondary text-secondary-foreground px-4 py-2 rounded-md hover:bg-secondary/80 transition-colors\"\n                      >\n                        管理文章\n                      </Link>\n                    </>\n                  )}\n                  <form action=\"/auth/signout\" method=\"post\">\n                    <button\n                      type=\"submit\"\n                      className=\"text-muted-foreground hover:text-foreground transition-colors\"\n                    >\n                      登出\n                    </button>\n                  </form>\n                </>\n              ) : (\n                <Link\n                  href=\"/login\"\n                  className=\"bg-primary text-primary-foreground px-4 py-2 rounded-md hover:bg-primary/90 transition-colors\"\n                >\n                  登入\n                </Link>\n              )}\n            </div>\n          </div>\n        </div>\n      </header>\n\n      {/* Main Content */}\n      <main className=\"max-w-4xl mx-auto px-4 py-8\">\n        {/* 返回按鈕 */}\n        <div className=\"mb-6\">\n          <Link\n            href=\"/\"\n            className=\"inline-flex items-center text-muted-foreground hover:text-foreground transition-colors\"\n          >\n            ← 返回文章列表\n          </Link>\n        </div>\n\n        {/* 文章內容 */}\n        <article className=\"bg-card rounded-lg shadow-sm border border-border p-8\">\n          {/* 文章標題 */}\n          <header className=\"mb-8\">\n            <h1 className=\"text-4xl font-bold text-card-foreground mb-4\">\n              {post.title}\n            </h1>\n            <div className=\"flex items-center gap-4 text-sm text-muted-foreground\">\n              <time>\n                發布於 {new Date(post.created_at).toLocaleDateString('zh-TW', {\n                  year: 'numeric',\n                  month: 'long',\n                  day: 'numeric'\n                })}\n              </time>\n              {post.updated_at !== post.created_at && (\n                <span>\n                  更新於 {new Date(post.updated_at).toLocaleDateString('zh-TW', {\n                    year: 'numeric',\n                    month: 'long',\n                    day: 'numeric'\n                  })}\n                </span>\n              )}\n            </div>\n          </header>\n\n          {/* 文章內容 */}\n          <div className=\"prose prose-lg max-w-none dark:prose-invert\">\n            <MarkdownRenderer content={post.content} />\n          </div>\n        </article>\n\n        {/* 管理員操作 */}\n        {userIsAdmin && (\n          <div className=\"mt-8 p-4 bg-muted rounded-lg\">\n            <h3 className=\"text-lg font-semibold text-foreground mb-4\">管理員操作</h3>\n            <div className=\"flex gap-4\">\n              <Link\n                href={`/admin/edit-post/${post.id}`}\n                className=\"bg-primary text-primary-foreground px-4 py-2 rounded-md hover:bg-primary/90 transition-colors\"\n              >\n                編輯文章\n              </Link>\n              <button\n                className=\"bg-destructive text-destructive-foreground px-4 py-2 rounded-md hover:bg-destructive/90 transition-colors\"\n                onClick={() => {\n                  if (confirm('確定要刪除這篇文章嗎？此操作無法復原。')) {\n                    // TODO: 實作刪除功能\n                    alert('刪除功能將在管理頁面中實作')\n                  }\n                }}\n              >\n                刪除文章\n              </button>\n            </div>\n          </div>\n        )}\n      </main>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAAA;AAAA;AACA;AACA;AACA;AAEA;AACA;;;;;;;;AAEA,eAAe,QAAQ,EAAU;IAC/B,MAAM,WAAW,MAAM,CAAA,GAAA,gIAAA,CAAA,eAAY,AAAD;IAClC,MAAM,EAAE,MAAM,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SACjC,IAAI,CAAC,SACL,MAAM,CAAC,KACP,EAAE,CAAC,MAAM,IACT,MAAM;IAET,IAAI,OAAO;QACT,QAAQ,KAAK,CAAC,wBAAwB;QACtC,OAAO;IACT;IAEA,OAAO;AACT;AAEe,eAAe,SAAS,EAAE,MAAM,EAAuC;IACpF,MAAM,EAAE,EAAE,EAAE,GAAG,MAAM;IACrB,MAAM,OAAO,MAAM,QAAQ;IAE3B,IAAI,CAAC,MAAM;QACT,CAAA,GAAA,qLAAA,CAAA,WAAQ,AAAD;IACT;IAEA,MAAM,OAAO,MAAM,CAAA,GAAA,kHAAA,CAAA,iBAAc,AAAD;IAChC,MAAM,cAAc,OAAO,MAAM,CAAA,GAAA,kHAAA,CAAA,UAAO,AAAD,EAAE,KAAK,EAAE,IAAI;IAEpD,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;0CACX;;;;;;0CAGD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,qIAAA,CAAA,cAAW;;;;;oCACX,qBACC;;0DACE,8OAAC;gDAAK,WAAU;;oDAAgC;oDACzC,KAAK,KAAK;;;;;;;4CAEhB,6BACC;;kEACE,8OAAC,4JAAA,CAAA,UAAI;wDACH,MAAK;wDACL,WAAU;kEACX;;;;;;kEAGD,8OAAC,4JAAA,CAAA,UAAI;wDACH,MAAK;wDACL,WAAU;kEACX;;;;;;;;0DAKL,8OAAC;gDAAK,QAAO;gDAAgB,QAAO;0DAClC,cAAA,8OAAC;oDACC,MAAK;oDACL,WAAU;8DACX;;;;;;;;;;;;qEAML,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAUX,8OAAC;gBAAK,WAAU;;kCAEd,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;4BACH,MAAK;4BACL,WAAU;sCACX;;;;;;;;;;;kCAMH,8OAAC;wBAAQ,WAAU;;0CAEjB,8OAAC;gCAAO,WAAU;;kDAChB,8OAAC;wCAAG,WAAU;kDACX,KAAK,KAAK;;;;;;kDAEb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;;oDAAK;oDACC,IAAI,KAAK,KAAK,UAAU,EAAE,kBAAkB,CAAC,SAAS;wDACzD,MAAM;wDACN,OAAO;wDACP,KAAK;oDACP;;;;;;;4CAED,KAAK,UAAU,KAAK,KAAK,UAAU,kBAClC,8OAAC;;oDAAK;oDACC,IAAI,KAAK,KAAK,UAAU,EAAE,kBAAkB,CAAC,SAAS;wDACzD,MAAM;wDACN,OAAO;wDACP,KAAK;oDACP;;;;;;;;;;;;;;;;;;;0CAOR,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,0IAAA,CAAA,mBAAgB;oCAAC,SAAS,KAAK,OAAO;;;;;;;;;;;;;;;;;oBAK1C,6BACC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAA6C;;;;;;0CAC3D,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAM,CAAC,iBAAiB,EAAE,KAAK,EAAE,EAAE;wCACnC,WAAU;kDACX;;;;;;kDAGD,8OAAC;wCACC,WAAU;wCACV,SAAS;4CACP,IAAI,QAAQ,wBAAwB;gDAClC,eAAe;gDACf,MAAM;4CACR;wCACF;kDACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASf", "debugId": null}}]}