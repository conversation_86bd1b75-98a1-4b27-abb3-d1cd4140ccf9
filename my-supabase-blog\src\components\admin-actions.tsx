'use client'

import { useRouter } from 'next/navigation'
import { createClient } from '@/lib/supabase/client'
import Link from 'next/link'

export function AdminActions({ postId }: { postId: string }) {
  const router = useRouter()
  const supabase = createClient()

  const handleDelete = async () => {
    if (!confirm('確定要刪除這篇文章嗎？此操作無法復原。')) return

    const { error } = await supabase
      .from('posts')
      .delete()
      .eq('id', postId)

    if (error) {
      alert('刪除文章時發生錯誤: ' + error.message)
    } else {
      alert('文章刪除成功！')
      router.push('/')
    }
  }

  return (
    <div className="flex gap-4">
      <Link
        href={`/admin/edit-post/${postId}`}
        className="bg-primary text-primary-foreground px-4 py-2 rounded-md hover:bg-primary/90 transition-colors"
      >
        編輯文章
      </Link>
      <button
        className="bg-destructive text-destructive-foreground px-4 py-2 rounded-md hover:bg-destructive/90 transition-colors"
        onClick={handleDelete}
      >
        刪除文章
      </button>
    </div>
  )
}