'use client'

interface MarkdownRendererProps {
  content: string
  className?: string
}

export function MarkdownRenderer({ content, className = '' }: MarkdownRendererProps) {
  // 簡單的 Markdown 渲染，將換行轉換為 <br>
  const renderContent = (text: string) => {
    return text
      .split('\n')
      .map((line, index) => (
        <div key={index} className="mb-2">
          {line || <br />}
        </div>
      ))
  }

  return (
    <div className={`markdown-renderer ${className}`}>
      <div className="prose prose-lg max-w-none dark:prose-invert text-foreground">
        {renderContent(content)}
      </div>
    </div>
  )
}
