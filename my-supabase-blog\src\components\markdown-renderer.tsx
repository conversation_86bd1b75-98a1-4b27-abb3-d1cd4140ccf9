'use client'

import { useEffect, useRef, useState } from 'react'
import { useTheme } from 'next-themes'

interface MarkdownRendererProps {
  content: string
  className?: string
}

export function MarkdownRenderer({ content, className = '' }: MarkdownRendererProps) {
  const containerRef = useRef<HTMLDivElement>(null)
  const cherryRef = useRef<any>(null)
  const [isLoaded, setIsLoaded] = useState(false)
  const { theme } = useTheme()

  useEffect(() => {
    const loadCherry = async () => {
      try {
        // 動態導入 Cherry Markdown
        const Cherry = (await import('cherry-markdown')).default
        
        if (containerRef.current && !cherryRef.current) {
          // 為渲染器容器設置唯一 ID
          const rendererId = `cherry-renderer-${Date.now()}`
          containerRef.current.id = rendererId

          cherryRef.current = new Cherry({
            id: rendererId,
            value: content,
            editor: {
              defaultModel: 'previewOnly'
            },
            toolbars: {
              showToolbar: false
            }
          } as any)
          
          setIsLoaded(true)
        }
      } catch (error) {
        console.error('Failed to load Cherry Markdown:', error)
        // 如果 Cherry 載入失敗，顯示純文字
        if (containerRef.current) {
          containerRef.current.innerHTML = `<pre class="whitespace-pre-wrap">${content}</pre>`
          setIsLoaded(true)
        }
      }
    }

    loadCherry()

    return () => {
      if (cherryRef.current) {
        cherryRef.current.destroy()
        cherryRef.current = null
      }
    }
  }, [])

  // 更新內容
  useEffect(() => {
    if (cherryRef.current && isLoaded) {
      try {
        cherryRef.current.setValue(content)
      } catch (error) {
        console.error('Failed to update content:', error)
      }
    }
  }, [content, isLoaded])

  // 更新主題
  useEffect(() => {
    if (cherryRef.current && isLoaded) {
      try {
        cherryRef.current.setTheme(theme === 'dark' ? 'dark' : 'light')
      } catch (error) {
        console.error('Failed to update theme:', error)
      }
    }
  }, [theme, isLoaded])

  return (
    <div className={`markdown-renderer ${className}`}>
      <div 
        ref={containerRef}
        className="prose prose-lg max-w-none dark:prose-invert"
      />
      {!isLoaded && (
        <div className="flex items-center justify-center h-32 bg-muted rounded-md">
          <div className="text-muted-foreground">載入內容中...</div>
        </div>
      )}
    </div>
  )
}
