{"version": 3, "sources": [], "sections": [{"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test2/my-supabase-blog/src/components/cherry-editor.tsx"], "sourcesContent": ["'use client'\n\nimport dynamic from 'next/dynamic'\nimport { useState, useEffect } from 'react'\nimport '@uiw/react-md-editor/markdown-editor.css'\nimport '@uiw/react-markdown-preview/markdown.css'\n\n// Dynamically import MDEditor to avoid SSR issues\nconst MDEditor = dynamic(\n  () => import('@uiw/react-md-editor'),\n  { ssr: false }\n)\n\ninterface CherryEditorProps {\n  value?: string\n  onChange?: (value: string) => void\n  height?: string\n  placeholder?: string\n}\n\nexport function CherryEditor({\n  value = '',\n  onChange,\n  height = '500px',\n  placeholder = '開始編寫您的文章...'\n}: CherryEditorProps) {\n  const [mounted, setMounted] = useState(false)\n\n  useEffect(() => {\n    setMounted(true)\n  }, [])\n\n  // Show loading state during SSR\n  if (!mounted) {\n    return (\n      <div\n        className=\"w-full p-4 border border-input bg-background text-foreground rounded-md animate-pulse\"\n        style={{ height }}\n      >\n        <div className=\"text-muted-foreground\">載入編輯器中...</div>\n      </div>\n    )\n  }\n\n  return (\n    <div className=\"w-full\" style={{ height }} data-color-mode=\"auto\">\n      <MDEditor\n        value={value}\n        onChange={(val) => onChange?.(val || '')}\n        height={parseInt(height.replace('px', ''))}\n        preview=\"live\"\n        hideToolbar={false}\n        visibleDragbar={false}\n        textareaProps={{\n          placeholder,\n          style: {\n            fontSize: 14,\n            lineHeight: 1.6,\n            fontFamily: 'ui-monospace, SFMono-Regular, \"SF Mono\", Monaco, Consolas, \"Liberation Mono\", \"Courier New\", monospace'\n          }\n        }}\n      />\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;;AAHA;;;;;;AAOA,kDAAkD;AAClD,MAAM,WAAW,CAAA,GAAA,+JAAA,CAAA,UAAO,AAAD;;;;;;IAEnB,KAAK;;AAUF,SAAS,aAAa,EAC3B,QAAQ,EAAE,EACV,QAAQ,EACR,SAAS,OAAO,EAChB,cAAc,aAAa,EACT;IAClB,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,WAAW;IACb,GAAG,EAAE;IAEL,gCAAgC;IAChC,IAAI,CAAC,SAAS;QACZ,qBACE,8OAAC;YACC,WAAU;YACV,OAAO;gBAAE;YAAO;sBAEhB,cAAA,8OAAC;gBAAI,WAAU;0BAAwB;;;;;;;;;;;IAG7C;IAEA,qBACE,8OAAC;QAAI,WAAU;QAAS,OAAO;YAAE;QAAO;QAAG,mBAAgB;kBACzD,cAAA,8OAAC;YACC,OAAO;YACP,UAAU,CAAC,MAAQ,WAAW,OAAO;YACrC,QAAQ,SAAS,OAAO,OAAO,CAAC,MAAM;YACtC,SAAQ;YACR,aAAa;YACb,gBAAgB;YAChB,eAAe;gBACb;gBACA,OAAO;oBACL,UAAU;oBACV,YAAY;oBACZ,YAAY;gBACd;YACF;;;;;;;;;;;AAIR", "debugId": null}}, {"offset": {"line": 205, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test2/my-supabase-blog/src/lib/supabase/client.ts"], "sourcesContent": ["import { createBrowserClient } from '@supabase/ssr'\n\nexport function createClient() {\n  return createBrowserClient(\n    process.env.NEXT_PUBLIC_SUPABASE_URL!,\n    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!\n  )\n}\n"], "names": [], "mappings": ";;;AAAA;AAAA;;AAEO,SAAS;IACd,OAAO,CAAA,GAAA,0KAAA,CAAA,sBAAmB,AAAD;AAI3B", "debugId": null}}, {"offset": {"line": 220, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test2/my-supabase-blog/src/app/admin/edit-post/%5Bid%5D/edit-post-form.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { useRouter } from 'next/navigation'\nimport Link from 'next/link'\nimport { CherryEditor } from '@/components/cherry-editor'\nimport { createClient } from '@/lib/supabase/client'\nimport { Post } from '@/types/database'\n\ninterface EditPostFormProps {\n  post: Post\n}\n\nexport default function EditPostForm({ post }: EditPostFormProps) {\n  const [title, setTitle] = useState(post.title)\n  const [content, setContent] = useState(post.content)\n  const [isSubmitting, setIsSubmitting] = useState(false)\n  const [error, setError] = useState('')\n  const router = useRouter()\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault()\n    \n    if (!title.trim() || !content.trim()) {\n      setError('標題和內容都是必填的')\n      return\n    }\n\n    setIsSubmitting(true)\n    setError('')\n\n    try {\n      const supabase = createClient()\n      \n      const { error: updateError } = await supabase\n        .from('posts')\n        .update({\n          title: title.trim(),\n          content: content.trim(),\n          updated_at: new Date().toISOString()\n        })\n        .eq('id', post.id)\n\n      if (updateError) {\n        throw updateError\n      }\n\n      // 成功後重定向到文章頁面\n      router.push(`/posts/${post.id}`)\n      router.refresh()\n    } catch (err) {\n      console.error('Error updating post:', err)\n      setError('更新文章時發生錯誤，請稍後再試')\n    } finally {\n      setIsSubmitting(false)\n    }\n  }\n\n  return (\n    <form onSubmit={handleSubmit} className=\"space-y-6\">\n      {error && (\n        <div className=\"bg-destructive/10 border border-destructive/20 text-destructive px-4 py-3 rounded-md\">\n          {error}\n        </div>\n      )}\n      \n      <div>\n        <label htmlFor=\"title\" className=\"block text-sm font-medium text-foreground mb-2\">\n          文章標題\n        </label>\n        <input\n          type=\"text\"\n          id=\"title\"\n          value={title}\n          onChange={(e) => setTitle(e.target.value)}\n          className=\"w-full px-3 py-2 border border-input bg-background text-foreground rounded-md focus:outline-none focus:ring-2 focus:ring-ring focus:border-transparent\"\n          placeholder=\"輸入文章標題...\"\n          disabled={isSubmitting}\n        />\n      </div>\n\n      <div>\n        <label htmlFor=\"content\" className=\"block text-sm font-medium text-foreground mb-2\">\n          文章內容\n        </label>\n        <div className=\"border border-input rounded-md overflow-hidden\">\n          <CherryEditor\n            value={content}\n            onChange={setContent}\n            height=\"600px\"\n            placeholder=\"編輯您的文章內容...\"\n          />\n        </div>\n      </div>\n\n      <div className=\"flex items-center gap-4\">\n        <button\n          type=\"submit\"\n          disabled={isSubmitting || !title.trim() || !content.trim()}\n          className=\"bg-primary text-primary-foreground px-6 py-2 rounded-md hover:bg-primary/90 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\"\n        >\n          {isSubmitting ? '更新中...' : '更新文章'}\n        </button>\n        \n        <Link\n          href={`/posts/${post.id}`}\n          className=\"bg-secondary text-secondary-foreground px-6 py-2 rounded-md hover:bg-secondary/80 transition-colors\"\n        >\n          取消\n        </Link>\n        \n        <Link\n          href=\"/admin/manage-posts\"\n          className=\"text-muted-foreground hover:text-foreground transition-colors\"\n        >\n          返回管理頁面\n        </Link>\n      </div>\n    </form>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AANA;;;;;;;AAae,SAAS,aAAa,EAAE,IAAI,EAAqB;IAC9D,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,KAAK,KAAK;IAC7C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,KAAK,OAAO;IACnD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAEhB,IAAI,CAAC,MAAM,IAAI,MAAM,CAAC,QAAQ,IAAI,IAAI;YACpC,SAAS;YACT;QACF;QAEA,gBAAgB;QAChB,SAAS;QAET,IAAI;YACF,MAAM,WAAW,CAAA,GAAA,gIAAA,CAAA,eAAY,AAAD;YAE5B,MAAM,EAAE,OAAO,WAAW,EAAE,GAAG,MAAM,SAClC,IAAI,CAAC,SACL,MAAM,CAAC;gBACN,OAAO,MAAM,IAAI;gBACjB,SAAS,QAAQ,IAAI;gBACrB,YAAY,IAAI,OAAO,WAAW;YACpC,GACC,EAAE,CAAC,MAAM,KAAK,EAAE;YAEnB,IAAI,aAAa;gBACf,MAAM;YACR;YAEA,cAAc;YACd,OAAO,IAAI,CAAC,CAAC,OAAO,EAAE,KAAK,EAAE,EAAE;YAC/B,OAAO,OAAO;QAChB,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,wBAAwB;YACtC,SAAS;QACX,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,qBACE,8OAAC;QAAK,UAAU;QAAc,WAAU;;YACrC,uBACC,8OAAC;gBAAI,WAAU;0BACZ;;;;;;0BAIL,8OAAC;;kCACC,8OAAC;wBAAM,SAAQ;wBAAQ,WAAU;kCAAiD;;;;;;kCAGlF,8OAAC;wBACC,MAAK;wBACL,IAAG;wBACH,OAAO;wBACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;wBACxC,WAAU;wBACV,aAAY;wBACZ,UAAU;;;;;;;;;;;;0BAId,8OAAC;;kCACC,8OAAC;wBAAM,SAAQ;wBAAU,WAAU;kCAAiD;;;;;;kCAGpF,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,sIAAA,CAAA,eAAY;4BACX,OAAO;4BACP,UAAU;4BACV,QAAO;4BACP,aAAY;;;;;;;;;;;;;;;;;0BAKlB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBACC,MAAK;wBACL,UAAU,gBAAgB,CAAC,MAAM,IAAI,MAAM,CAAC,QAAQ,IAAI;wBACxD,WAAU;kCAET,eAAe,WAAW;;;;;;kCAG7B,8OAAC,4JAAA,CAAA,UAAI;wBACH,MAAM,CAAC,OAAO,EAAE,KAAK,EAAE,EAAE;wBACzB,WAAU;kCACX;;;;;;kCAID,8OAAC,4JAAA,CAAA,UAAI;wBACH,MAAK;wBACL,WAAU;kCACX;;;;;;;;;;;;;;;;;;AAMT", "debugId": null}}]}