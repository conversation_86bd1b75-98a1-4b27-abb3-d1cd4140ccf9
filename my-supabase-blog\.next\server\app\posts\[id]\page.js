(()=>{var e={};e.id=880,e.ids=[880],e.modules={490:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>i.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>m,tree:()=>l});var s=t(5239),n=t(8088),o=t(8170),i=t.n(o),a=t(893),d={};for(let e in a)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>a[e]);t.d(r,d);let l={children:["",{children:["posts",{children:["[id]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,8320)),"C:\\Users\\<USER>\\Desktop\\test2\\my-supabase-blog\\src\\app\\posts\\[id]\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,4431)),"C:\\Users\\<USER>\\Desktop\\test2\\my-supabase-blog\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,5284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\Desktop\\test2\\my-supabase-blog\\src\\app\\posts\\[id]\\page.tsx"],u={require:t,loadChunk:()=>Promise.resolve()},m=new s.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/posts/[id]/page",pathname:"/posts/[id]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1339:(e,r,t)=>{"use strict";t.d(r,{ThemeToggle:()=>l});var s=t(687),n=t(3210),o=t(2688);let i=(0,o.A)("sun",[["circle",{cx:"12",cy:"12",r:"4",key:"4exip2"}],["path",{d:"M12 2v2",key:"tus03m"}],["path",{d:"M12 20v2",key:"1lh1kg"}],["path",{d:"m4.93 4.93 1.41 1.41",key:"149t6j"}],["path",{d:"m17.66 17.66 1.41 1.41",key:"ptbguv"}],["path",{d:"M2 12h2",key:"1t8f8n"}],["path",{d:"M20 12h2",key:"1q8mjw"}],["path",{d:"m6.34 17.66-1.41 1.41",key:"1m8zz5"}],["path",{d:"m19.07 4.93-1.41 1.41",key:"1shlcs"}]]),a=(0,o.A)("moon",[["path",{d:"M12 3a6 6 0 0 0 9 9 9 9 0 1 1-9-9Z",key:"a7tn18"}]]);var d=t(218);function l(){let{theme:e,setTheme:r}=(0,d.D)(),[t,o]=n.useState(!1);return(n.useEffect(()=>{o(!0)},[]),t)?(0,s.jsxs)("button",{onClick:()=>r("light"===e?"dark":"light"),className:"inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none ring-offset-background border border-input bg-background hover:bg-accent hover:text-accent-foreground h-10 w-10",children:["light"===e?(0,s.jsx)(a,{className:"h-[1.2rem] w-[1.2rem]"}):(0,s.jsx)(i,{className:"h-[1.2rem] w-[1.2rem]"}),(0,s.jsx)("span",{className:"sr-only",children:"切換主題"})]}):(0,s.jsxs)("button",{className:"inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none ring-offset-background border border-input bg-background hover:bg-accent hover:text-accent-foreground h-10 w-10",children:[(0,s.jsx)(i,{className:"h-[1.2rem] w-[1.2rem]"}),(0,s.jsx)("span",{className:"sr-only",children:"切換主題"})]})}},1630:e=>{"use strict";e.exports=require("http")},1645:e=>{"use strict";e.exports=require("net")},1997:e=>{"use strict";e.exports=require("punycode")},2506:(e,r,t)=>{"use strict";t.d(r,{ThemeToggle:()=>s});let s=(0,t(2907).registerClientReference)(function(){throw Error("Attempted to call ThemeToggle() from the server but ThemeToggle is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\test2\\my-supabase-blog\\src\\components\\theme-toggle.tsx","ThemeToggle")},2688:(e,r,t)=>{"use strict";t.d(r,{A:()=>u});var s=t(3210);let n=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),o=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,r,t)=>t?t.toUpperCase():r.toLowerCase()),i=e=>{let r=o(e);return r.charAt(0).toUpperCase()+r.slice(1)},a=(...e)=>e.filter((e,r,t)=>!!e&&""!==e.trim()&&t.indexOf(e)===r).join(" ").trim(),d=e=>{for(let r in e)if(r.startsWith("aria-")||"role"===r||"title"===r)return!0};var l={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let c=(0,s.forwardRef)(({color:e="currentColor",size:r=24,strokeWidth:t=2,absoluteStrokeWidth:n,className:o="",children:i,iconNode:c,...u},m)=>(0,s.createElement)("svg",{ref:m,...l,width:r,height:r,stroke:e,strokeWidth:n?24*Number(t)/Number(r):t,className:a("lucide",o),...!i&&!d(u)&&{"aria-hidden":"true"},...u},[...c.map(([e,r])=>(0,s.createElement)(e,r)),...Array.isArray(i)?i:[i]])),u=(e,r)=>{let t=(0,s.forwardRef)(({className:t,...o},d)=>(0,s.createElement)(c,{ref:d,iconNode:r,className:a(`lucide-${n(i(e))}`,`lucide-${e}`,t),...o}));return t.displayName=i(e),t}},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3465:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,5814,23)),Promise.resolve().then(t.bind(t,4167)),Promise.resolve().then(t.bind(t,1339))},3873:e=>{"use strict";e.exports=require("path")},4075:e=>{"use strict";e.exports=require("zlib")},4167:(e,r,t)=>{"use strict";t.d(r,{MarkdownRenderer:()=>i});var s=t(687),n=t(3210),o=t(218);function i({content:e,className:r=""}){let t=(0,n.useRef)(null);(0,n.useRef)(null);let[i,a]=(0,n.useState)(!1),{theme:d}=(0,o.D)();return(0,s.jsxs)("div",{className:`markdown-renderer ${r}`,children:[(0,s.jsx)("div",{ref:t,className:"prose prose-lg max-w-none dark:prose-invert"}),!i&&(0,s.jsx)("div",{className:"flex items-center justify-center h-32 bg-muted rounded-md",children:(0,s.jsx)("div",{className:"text-muted-foreground",children:"載入內容中..."})})]})}},4389:(e,r,t)=>{"use strict";t.d(r,{MarkdownRenderer:()=>s});let s=(0,t(2907).registerClientReference)(function(){throw Error("Attempted to call MarkdownRenderer() from the server but MarkdownRenderer is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\test2\\my-supabase-blog\\src\\components\\markdown-renderer.tsx","MarkdownRenderer")},4536:(e,r,t)=>{let{createProxy:s}=t(9844);e.exports=s("C:\\Users\\<USER>\\Desktop\\test2\\my-supabase-blog\\node_modules\\next\\dist\\client\\app-dir\\link.js")},4631:e=>{"use strict";e.exports=require("tls")},4735:e=>{"use strict";e.exports=require("events")},5511:e=>{"use strict";e.exports=require("crypto")},5591:e=>{"use strict";e.exports=require("https")},7910:e=>{"use strict";e.exports=require("stream")},8320:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>m});var s=t(7413),n=t(9916),o=t(4536),i=t.n(o),a=t(2507),d=t(2909),l=t(4389),c=t(2506);async function u(e){let r=await (0,a.U)(),{data:t,error:s}=await r.from("posts").select("*").eq("id",e).single();return s?(console.error("Error fetching post:",s),null):t}async function m({params:e}){let{id:r}=await e,t=await u(r);t||(0,n.notFound)();let o=await (0,d.HW)(),a=!!o&&await (0,d.qc)(o.id);return(0,s.jsxs)("div",{className:"min-h-screen bg-background",children:[(0,s.jsx)("header",{className:"bg-card shadow-sm border-b border-border",children:(0,s.jsx)("div",{className:"max-w-4xl mx-auto px-4 py-6",children:(0,s.jsxs)("div",{className:"flex justify-between items-center",children:[(0,s.jsx)(i(),{href:"/",className:"text-2xl font-bold text-foreground hover:text-primary transition-colors",children:"我的部落格"}),(0,s.jsxs)("div",{className:"flex items-center gap-4",children:[(0,s.jsx)(c.ThemeToggle,{}),o?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)("span",{className:"text-sm text-muted-foreground",children:["歡迎, ",o.email]}),a&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(i(),{href:"/admin/new-post",className:"bg-primary text-primary-foreground px-4 py-2 rounded-md hover:bg-primary/90 transition-colors",children:"新增文章"}),(0,s.jsx)(i(),{href:"/admin/manage-posts",className:"bg-secondary text-secondary-foreground px-4 py-2 rounded-md hover:bg-secondary/80 transition-colors",children:"管理文章"})]}),(0,s.jsx)("form",{action:"/auth/signout",method:"post",children:(0,s.jsx)("button",{type:"submit",className:"text-muted-foreground hover:text-foreground transition-colors",children:"登出"})})]}):(0,s.jsx)(i(),{href:"/login",className:"bg-primary text-primary-foreground px-4 py-2 rounded-md hover:bg-primary/90 transition-colors",children:"登入"})]})]})})}),(0,s.jsxs)("main",{className:"max-w-4xl mx-auto px-4 py-8",children:[(0,s.jsx)("div",{className:"mb-6",children:(0,s.jsx)(i(),{href:"/",className:"inline-flex items-center text-muted-foreground hover:text-foreground transition-colors",children:"← 返回文章列表"})}),(0,s.jsxs)("article",{className:"bg-card rounded-lg shadow-sm border border-border p-8",children:[(0,s.jsxs)("header",{className:"mb-8",children:[(0,s.jsx)("h1",{className:"text-4xl font-bold text-card-foreground mb-4",children:t.title}),(0,s.jsxs)("div",{className:"flex items-center gap-4 text-sm text-muted-foreground",children:[(0,s.jsxs)("time",{children:["發布於 ",new Date(t.created_at).toLocaleDateString("zh-TW",{year:"numeric",month:"long",day:"numeric"})]}),t.updated_at!==t.created_at&&(0,s.jsxs)("span",{children:["更新於 ",new Date(t.updated_at).toLocaleDateString("zh-TW",{year:"numeric",month:"long",day:"numeric"})]})]})]}),(0,s.jsx)("div",{className:"prose prose-lg max-w-none dark:prose-invert",children:(0,s.jsx)(l.MarkdownRenderer,{content:t.content})})]}),a&&(0,s.jsxs)("div",{className:"mt-8 p-4 bg-muted rounded-lg",children:[(0,s.jsx)("h3",{className:"text-lg font-semibold text-foreground mb-4",children:"管理員操作"}),(0,s.jsxs)("div",{className:"flex gap-4",children:[(0,s.jsx)(i(),{href:`/admin/edit-post/${t.id}`,className:"bg-primary text-primary-foreground px-4 py-2 rounded-md hover:bg-primary/90 transition-colors",children:"編輯文章"}),(0,s.jsx)("button",{className:"bg-destructive text-destructive-foreground px-4 py-2 rounded-md hover:bg-destructive/90 transition-colors",onClick:()=>{confirm("確定要刪除這篇文章嗎？此操作無法復原。")&&alert("刪除功能將在管理頁面中實作")},children:"刪除文章"})]})]})]})]})}},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9398:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,4536,23)),Promise.resolve().then(t.bind(t,4389)),Promise.resolve().then(t.bind(t,2506))},9428:e=>{"use strict";e.exports=require("buffer")},9551:e=>{"use strict";e.exports=require("url")}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[447,98,866,567,924],()=>t(490));module.exports=s})();