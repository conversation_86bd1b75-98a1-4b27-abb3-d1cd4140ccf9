(()=>{var e={};e.id=466,e.ids=[466],e.modules={846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1365:(e,t,r)=>{"use strict";r.d(t,{D:()=>n});var s=r(687),i=r(3210),o=r(218);function n({value:e="",onChange:t,height:r="500px",placeholder:n="開始編寫您的文章..."}){let a=(0,i.useRef)(null);(0,i.useRef)(null);let[d,l]=(0,i.useState)(!1),{theme:u}=(0,o.D)();return(0,s.jsxs)("div",{className:"cherry-editor-wrapper",children:[(0,s.jsx)("div",{ref:a,className:"cherry-editor",style:{minHeight:r}}),!d&&(0,s.jsx)("div",{className:"flex items-center justify-center h-64 bg-muted rounded-md",children:(0,s.jsx)("div",{className:"text-muted-foreground",children:"載入編輯器中..."})})]})}},1595:(e,t,r)=>{"use strict";r.d(t,{default:()=>u});var s=r(687),i=r(3210),o=r(6189),n=r(5814),a=r.n(n),d=r(1365),l=r(9481);function u({post:e}){let[t,r]=(0,i.useState)(e.title),[n,u]=(0,i.useState)(e.content),[c,p]=(0,i.useState)(!1),[m,x]=(0,i.useState)(""),h=(0,o.useRouter)(),f=async r=>{if(r.preventDefault(),!t.trim()||!n.trim())return void x("標題和內容都是必填的");p(!0),x("");try{let r=(0,l.U)(),{error:s}=await r.from("posts").update({title:t.trim(),content:n.trim(),updated_at:new Date().toISOString()}).eq("id",e.id);if(s)throw s;h.push(`/posts/${e.id}`),h.refresh()}catch(e){console.error("Error updating post:",e),x("更新文章時發生錯誤，請稍後再試")}finally{p(!1)}};return(0,s.jsxs)("form",{onSubmit:f,className:"space-y-6",children:[m&&(0,s.jsx)("div",{className:"bg-destructive/10 border border-destructive/20 text-destructive px-4 py-3 rounded-md",children:m}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"title",className:"block text-sm font-medium text-foreground mb-2",children:"文章標題"}),(0,s.jsx)("input",{type:"text",id:"title",value:t,onChange:e=>r(e.target.value),className:"w-full px-3 py-2 border border-input bg-background text-foreground rounded-md focus:outline-none focus:ring-2 focus:ring-ring focus:border-transparent",placeholder:"輸入文章標題...",disabled:c})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"content",className:"block text-sm font-medium text-foreground mb-2",children:"文章內容"}),(0,s.jsx)("div",{className:"border border-input rounded-md overflow-hidden",children:(0,s.jsx)(d.D,{value:n,onChange:u,height:"600px",placeholder:"編輯您的文章內容..."})})]}),(0,s.jsxs)("div",{className:"flex items-center gap-4",children:[(0,s.jsx)("button",{type:"submit",disabled:c||!t.trim()||!n.trim(),className:"bg-primary text-primary-foreground px-6 py-2 rounded-md hover:bg-primary/90 disabled:opacity-50 disabled:cursor-not-allowed transition-colors",children:c?"更新中...":"更新文章"}),(0,s.jsx)(a(),{href:`/posts/${e.id}`,className:"bg-secondary text-secondary-foreground px-6 py-2 rounded-md hover:bg-secondary/80 transition-colors",children:"取消"}),(0,s.jsx)(a(),{href:"/admin/manage-posts",className:"text-muted-foreground hover:text-foreground transition-colors",children:"返回管理頁面"})]})]})}},1630:e=>{"use strict";e.exports=require("http")},1645:e=>{"use strict";e.exports=require("net")},1997:e=>{"use strict";e.exports=require("punycode")},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3110:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>n.a,__next_app__:()=>c,pages:()=>u,routeModule:()=>p,tree:()=>l});var s=r(5239),i=r(8088),o=r(8170),n=r.n(o),a=r(893),d={};for(let e in a)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>a[e]);r.d(t,d);let l={children:["",{children:["admin",{children:["edit-post",{children:["[id]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,6062)),"C:\\Users\\<USER>\\Desktop\\test2\\my-supabase-blog\\src\\app\\admin\\edit-post\\[id]\\page.tsx"]}]},{}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,4431)),"C:\\Users\\<USER>\\Desktop\\test2\\my-supabase-blog\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,5284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,u=["C:\\Users\\<USER>\\Desktop\\test2\\my-supabase-blog\\src\\app\\admin\\edit-post\\[id]\\page.tsx"],c={require:r,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/admin/edit-post/[id]/page",pathname:"/admin/edit-post/[id]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3873:e=>{"use strict";e.exports=require("path")},4075:e=>{"use strict";e.exports=require("zlib")},4631:e=>{"use strict";e.exports=require("tls")},4735:e=>{"use strict";e.exports=require("events")},5161:(e,t,r)=>{"use strict";r.d(t,{default:()=>s});let s=(0,r(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test2\\\\my-supabase-blog\\\\src\\\\app\\\\admin\\\\edit-post\\\\[id]\\\\edit-post-form.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\test2\\my-supabase-blog\\src\\app\\admin\\edit-post\\[id]\\edit-post-form.tsx","default")},5198:(e,t,r)=>{Promise.resolve().then(r.bind(r,1595))},5462:(e,t,r)=>{Promise.resolve().then(r.bind(r,5161))},5511:e=>{"use strict";e.exports=require("crypto")},5591:e=>{"use strict";e.exports=require("https")},6062:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>l});var s=r(7413),i=r(9916),o=r(2909),n=r(2507),a=r(5161);async function d(e){let t=await (0,n.U)(),{data:r,error:s}=await t.from("posts").select("*").eq("id",e).single();return s?(console.error("Error fetching post:",s),null):r}async function l({params:e}){let{id:t}=await e,r=await (0,o.HW)();r||(0,i.redirect)("/login"),await (0,o.qc)(r.id)||(0,i.redirect)("/");let n=await d(t);return n||(0,i.notFound)(),(0,s.jsx)("div",{className:"min-h-screen bg-background",children:(0,s.jsxs)("div",{className:"max-w-4xl mx-auto px-4 py-8",children:[(0,s.jsxs)("div",{className:"mb-8",children:[(0,s.jsx)("h1",{className:"text-3xl font-bold text-foreground mb-2",children:"編輯文章"}),(0,s.jsx)("p",{className:"text-muted-foreground",children:"修改文章內容"})]}),(0,s.jsx)(a.default,{post:n})]})})}},7910:e=>{"use strict";e.exports=require("stream")},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9428:e=>{"use strict";e.exports=require("buffer")},9481:(e,t,r)=>{"use strict";r.d(t,{U:()=>i});var s=r(9522);function i(){return(0,s.createBrowserClient)("https://sqoixvgmroejgaebxyeq.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InNxb2l4dmdtcm9lamdhZWJ4eWVxIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTExNjU0ODksImV4cCI6MjA2Njc0MTQ4OX0.SFkF9v0Y-lrJuQHiSf5HLTLuwgOXRf0ERQKnfQNfLsU")}},9551:e=>{"use strict";e.exports=require("url")}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[447,98,866,567,318,924],()=>r(3110));module.exports=s})();