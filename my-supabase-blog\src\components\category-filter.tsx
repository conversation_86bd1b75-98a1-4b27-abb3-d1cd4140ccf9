'use client'

import { useState, useMemo, useEffect, useRef } from 'react'
import { Post } from '@/types/database'
import { cn } from '@/lib/utils'

interface CategoryFilterProps {
  posts: Post[]
  onFilterChange: (filteredPosts: Post[]) => void
  className?: string
}

// Define categories based on common keywords
const CATEGORIES = [
  {
    id: 'all',
    name: 'All Posts',
    keywords: [],
    icon: '📝'
  },
  {
    id: 'technology',
    name: 'Technology',
    keywords: ['tech', 'technology', 'programming', 'code', 'software', 'development', 'web', 'app', 'javascript', 'react', 'next', 'node', 'python', 'ai', 'machine learning', 'data', 'api', 'database'],
    icon: '💻'
  },
  {
    id: 'design',
    name: 'Design',
    keywords: ['design', 'ui', 'ux', 'interface', 'user experience', 'visual', 'graphic', 'layout', 'typography', 'color', 'branding', 'figma', 'sketch'],
    icon: '🎨'
  },
  {
    id: 'business',
    name: 'Business',
    keywords: ['business', 'startup', 'entrepreneur', 'marketing', 'strategy', 'growth', 'revenue', 'sales', 'management', 'leadership', 'productivity', 'finance'],
    icon: '💼'
  },
  {
    id: 'lifestyle',
    name: 'Lifestyle',
    keywords: ['lifestyle', 'health', 'fitness', 'travel', 'food', 'cooking', 'wellness', 'mindfulness', 'hobby', 'personal', 'life', 'tips', 'advice'],
    icon: '🌟'
  },
  {
    id: 'tutorial',
    name: 'Tutorials',
    keywords: ['tutorial', 'guide', 'how to', 'step by step', 'learn', 'beginner', 'introduction', 'getting started', 'walkthrough', 'example'],
    icon: '📚'
  },
  {
    id: 'news',
    name: 'News & Updates',
    keywords: ['news', 'update', 'announcement', 'release', 'new', 'latest', 'breaking', 'trending', 'current', 'recent'],
    icon: '📰'
  }
]

function categorizePost(post: Post): string[] {
  const content = (post.title + ' ' + post.content).toLowerCase()
  const categories: string[] = []

  for (const category of CATEGORIES) {
    if (category.id === 'all') continue
    
    const hasKeyword = category.keywords.some(keyword => 
      content.includes(keyword.toLowerCase())
    )
    
    if (hasKeyword) {
      categories.push(category.id)
    }
  }

  // If no categories found, categorize as 'other'
  return categories.length > 0 ? categories : ['other']
}

export function CategoryFilter({ posts, onFilterChange, className }: CategoryFilterProps) {
  const [selectedCategory, setSelectedCategory] = useState('all')
  const isInitialMount = useRef(true)

  // Calculate post counts for each category
  const categoryCounts = useMemo(() => {
    const counts: Record<string, number> = { all: posts.length }
    
    posts.forEach(post => {
      const postCategories = categorizePost(post)
      postCategories.forEach(categoryId => {
        counts[categoryId] = (counts[categoryId] || 0) + 1
      })
    })

    return counts
  }, [posts])

  // Filter posts based on selected category
  const filteredPosts = useMemo(() => {
    if (selectedCategory === 'all') {
      return posts
    }

    return posts.filter(post => {
      const postCategories = categorizePost(post)
      return postCategories.includes(selectedCategory)
    })
  }, [posts, selectedCategory])

  // Update parent component when filter changes
  useEffect(() => {
    if (isInitialMount.current) {
      isInitialMount.current = false
    } else {
      onFilterChange(filteredPosts)
    }
  }, [filteredPosts, onFilterChange])

  const handleCategoryChange = (categoryId: string) => {
    setSelectedCategory(categoryId)
  }

  return (
    <div className={cn("space-y-4", className)}>
      {/* Category Buttons */}
      <div className="flex flex-wrap gap-2">
        {CATEGORIES.map(category => {
          const count = categoryCounts[category.id] || 0
          const isActive = selectedCategory === category.id
          const isDisabled = count === 0 && category.id !== 'all'

          return (
            <button
              key={category.id}
              onClick={() => handleCategoryChange(category.id)}
              disabled={isDisabled}
              className={cn(
                "inline-flex items-center gap-2 px-3 py-2 rounded-full text-sm font-medium transition-all duration-200",
                "border border-border hover:border-primary/30",
                "focus:outline-none focus:ring-2 focus:ring-primary/20",
                isActive
                  ? "bg-primary text-primary-foreground border-primary shadow-sm"
                  : isDisabled
                  ? "bg-muted/50 text-muted-foreground cursor-not-allowed opacity-50"
                  : "bg-background text-foreground hover:bg-muted hover:shadow-sm"
              )}
            >
              <span className="text-base">{category.icon}</span>
              <span>{category.name}</span>
              {count > 0 && (
                <span className={cn(
                  "px-2 py-0.5 rounded-full text-xs font-semibold",
                  isActive
                    ? "bg-primary-foreground/20 text-primary-foreground"
                    : "bg-muted text-muted-foreground"
                )}>
                  {count}
                </span>
              )}
            </button>
          )
        })}
      </div>

      {/* Active Filter Info */}
      {selectedCategory !== 'all' && (
        <div className="flex items-center justify-between p-3 bg-muted/50 rounded-lg border border-border">
          <div className="flex items-center gap-2">
            <span className="text-lg">
              {CATEGORIES.find(c => c.id === selectedCategory)?.icon}
            </span>
            <span className="text-sm text-foreground">
              Showing <strong>{filteredPosts.length}</strong> posts in{' '}
              <strong>{CATEGORIES.find(c => c.id === selectedCategory)?.name}</strong>
            </span>
          </div>
          <button
            onClick={() => handleCategoryChange('all')}
            className="text-xs text-muted-foreground hover:text-foreground transition-colors underline"
          >
            Clear filter
          </button>
        </div>
      )}
    </div>
  )
}

// Hook to get post categories
export function usePostCategories(posts: Post[]) {
  return useMemo(() => {
    const postCategories = new Map<string, string[]>()
    
    posts.forEach(post => {
      const categories = categorizePost(post)
      postCategories.set(post.id, categories)
    })

    return postCategories
  }, [posts])
}

// Utility function to get category info
export function getCategoryInfo(categoryId: string) {
  return CATEGORIES.find(c => c.id === categoryId) || null
}

// Utility function to get all available categories
export function getAllCategories() {
  return CATEGORIES
}
