{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test2/my-supabase-blog/src/components/theme-toggle.tsx"], "sourcesContent": ["'use client'\n\nimport * as React from 'react'\nimport { Moon, Sun } from 'lucide-react'\nimport { useTheme } from 'next-themes'\n\nexport function ThemeToggle() {\n  const { theme, setTheme } = useTheme()\n  const [mounted, setMounted] = React.useState(false)\n\n  React.useEffect(() => {\n    setMounted(true)\n  }, [])\n\n  if (!mounted) {\n    return (\n      <button className=\"inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none ring-offset-background border border-input bg-background hover:bg-accent hover:text-accent-foreground h-10 w-10\">\n        <Sun className=\"h-[1.2rem] w-[1.2rem]\" />\n        <span className=\"sr-only\">切換主題</span>\n      </button>\n    )\n  }\n\n  return (\n    <button\n      onClick={() => setTheme(theme === 'light' ? 'dark' : 'light')}\n      className=\"inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none ring-offset-background border border-input bg-background hover:bg-accent hover:text-accent-foreground h-10 w-10\"\n    >\n      {theme === 'light' ? (\n        <Moon className=\"h-[1.2rem] w-[1.2rem]\" />\n      ) : (\n        <Sun className=\"h-[1.2rem] w-[1.2rem]\" />\n      )}\n      <span className=\"sr-only\">切換主題</span>\n    </button>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;;;AAJA;;;;AAMO,SAAS;;IACd,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,mJAAA,CAAA,WAAQ,AAAD;IACnC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAc,AAAD,EAAE;IAE7C,CAAA,GAAA,6JAAA,CAAA,YAAe,AAAD;iCAAE;YACd,WAAW;QACb;gCAAG,EAAE;IAEL,IAAI,CAAC,SAAS;QACZ,qBACE,6LAAC;YAAO,WAAU;;8BAChB,6LAAC,mMAAA,CAAA,MAAG;oBAAC,WAAU;;;;;;8BACf,6LAAC;oBAAK,WAAU;8BAAU;;;;;;;;;;;;IAGhC;IAEA,qBACE,6LAAC;QACC,SAAS,IAAM,SAAS,UAAU,UAAU,SAAS;QACrD,WAAU;;YAET,UAAU,wBACT,6LAAC,qMAAA,CAAA,OAAI;gBAAC,WAAU;;;;;qCAEhB,6LAAC,mMAAA,CAAA,MAAG;gBAAC,WAAU;;;;;;0BAEjB,6LAAC;gBAAK,WAAU;0BAAU;;;;;;;;;;;;AAGhC;GA9BgB;;QACc,mJAAA,CAAA,WAAQ;;;KADtB", "debugId": null}}, {"offset": {"line": 105, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test2/my-supabase-blog/src/lib/supabase/client.ts"], "sourcesContent": ["import { createBrowserClient } from '@supabase/ssr'\n\nexport function createClient() {\n  return createBrowserClient(\n    process.env.NEXT_PUBLIC_SUPABASE_URL!,\n    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!\n  )\n}\n"], "names": [], "mappings": ";;;AAII;AAJJ;AAAA;;AAEO,SAAS;IACd,OAAO,CAAA,GAAA,6KAAA,CAAA,sBAAmB,AAAD;AAI3B", "debugId": null}}, {"offset": {"line": 124, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test2/my-supabase-blog/src/app/admin/manage-posts/manage-posts-client.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { useRouter } from 'next/navigation'\nimport Link from 'next/link'\nimport { Post } from '@/types/database'\nimport { createClient } from '@/lib/supabase/client'\nimport { Trash2, Edit, Eye, Calendar } from 'lucide-react'\n\ninterface ManagePostsClientProps {\n  posts: Post[]\n}\n\nexport default function ManagePostsClient({ posts: initialPosts }: ManagePostsClientProps) {\n  const [posts, setPosts] = useState(initialPosts)\n  const [deletingId, setDeletingId] = useState<string | null>(null)\n  const router = useRouter()\n\n  const handleDelete = async (postId: string, title: string) => {\n    if (!confirm(`確定要刪除文章「${title}」嗎？此操作無法復原。`)) {\n      return\n    }\n\n    setDeletingId(postId)\n\n    try {\n      const supabase = createClient()\n      const { error } = await supabase\n        .from('posts')\n        .delete()\n        .eq('id', postId)\n\n      if (error) {\n        throw error\n      }\n\n      // 從本地狀態中移除已刪除的文章\n      setPosts(posts.filter(post => post.id !== postId))\n      \n      // 重新整理頁面以確保數據同步\n      router.refresh()\n    } catch (error) {\n      console.error('Error deleting post:', error)\n      alert('刪除文章時發生錯誤，請稍後再試')\n    } finally {\n      setDeletingId(null)\n    }\n  }\n\n  if (posts.length === 0) {\n    return (\n      <div className=\"text-center py-12\">\n        <h2 className=\"text-xl text-muted-foreground mb-4\">目前沒有文章</h2>\n        <Link\n          href=\"/admin/new-post\"\n          className=\"bg-primary text-primary-foreground px-6 py-3 rounded-md hover:bg-primary/90 transition-colors\"\n        >\n          建立第一篇文章\n        </Link>\n      </div>\n    )\n  }\n\n  return (\n    <div className=\"space-y-4\">\n      {/* 統計資訊 */}\n      <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6 mb-8\">\n        <div className=\"bg-card rounded-lg border border-border p-6 hover-lift animate-fade-in\">\n          <div className=\"flex items-center\">\n            <div className=\"p-3 bg-primary/10 rounded-xl\">\n              <svg className=\"h-6 w-6 text-primary\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\" />\n              </svg>\n            </div>\n            <div className=\"ml-4\">\n              <p className=\"text-sm font-medium text-muted-foreground\">總文章數</p>\n              <p className=\"text-3xl font-bold text-foreground\">{posts.length}</p>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"bg-card rounded-lg border border-border p-6 hover-lift animate-fade-in\" style={{ animationDelay: '0.1s' }}>\n          <div className=\"flex items-center\">\n            <div className=\"p-3 bg-green-100 dark:bg-green-900/20 rounded-xl\">\n              <svg className=\"h-6 w-6 text-green-600 dark:text-green-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\" />\n              </svg>\n            </div>\n            <div className=\"ml-4\">\n              <p className=\"text-sm font-medium text-muted-foreground\">今日發布</p>\n              <p className=\"text-3xl font-bold text-foreground\">\n                {posts.filter(post => {\n                  const today = new Date()\n                  const postDate = new Date(post.created_at)\n                  return postDate.toDateString() === today.toDateString()\n                }).length}\n              </p>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"bg-card rounded-lg border border-border p-6 hover-lift animate-fade-in\" style={{ animationDelay: '0.2s' }}>\n          <div className=\"flex items-center\">\n            <div className=\"p-3 bg-blue-100 dark:bg-blue-900/20 rounded-xl\">\n              <svg className=\"h-6 w-6 text-blue-600 dark:text-blue-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M13 7h8m0 0v8m0-8l-8 8-4-4-6 6\" />\n              </svg>\n            </div>\n            <div className=\"ml-4\">\n              <p className=\"text-sm font-medium text-muted-foreground\">本週發布</p>\n              <p className=\"text-3xl font-bold text-foreground\">\n                {posts.filter(post => {\n                  const weekAgo = new Date()\n                  weekAgo.setDate(weekAgo.getDate() - 7)\n                  return new Date(post.created_at) > weekAgo\n                }).length}\n              </p>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* 文章列表 */}\n      <div className=\"bg-card rounded-lg border border-border overflow-hidden\">\n        <div className=\"overflow-x-auto\">\n          <table className=\"w-full\">\n            <thead className=\"bg-muted\">\n              <tr>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider\">\n                  標題\n                </th>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider\">\n                  發布日期\n                </th>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider\">\n                  更新日期\n                </th>\n                <th className=\"px-6 py-3 text-right text-xs font-medium text-muted-foreground uppercase tracking-wider\">\n                  操作\n                </th>\n              </tr>\n            </thead>\n            <tbody className=\"divide-y divide-border\">\n              {posts.map((post, index) => (\n                <tr key={post.id} className=\"hover:bg-muted/50 transition-all duration-200 animate-slide-in\" style={{ animationDelay: `${index * 0.05}s` }}>\n                  <td className=\"px-6 py-4\">\n                    <div className=\"flex items-center\">\n                      <div>\n                        <div className=\"text-sm font-medium text-card-foreground\">\n                          {post.title}\n                        </div>\n                        <div className=\"text-sm text-muted-foreground\">\n                          {post.content.substring(0, 100)}\n                          {post.content.length > 100 && '...'}\n                        </div>\n                      </div>\n                    </div>\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap\">\n                    <div className=\"flex items-center text-sm text-muted-foreground\">\n                      <Calendar className=\"w-4 h-4 mr-2\" />\n                      {new Date(post.created_at).toLocaleDateString('zh-TW')}\n                    </div>\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap\">\n                    <div className=\"text-sm text-muted-foreground\">\n                      {new Date(post.updated_at).toLocaleDateString('zh-TW')}\n                    </div>\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap text-right text-sm font-medium\">\n                    <div className=\"flex items-center justify-end gap-2\">\n                      <Link\n                        href={`/posts/${post.id}`}\n                        className=\"text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 transition-all duration-200 p-2 rounded-lg hover:bg-blue-50 dark:hover:bg-blue-900/20 group\"\n                        title=\"查看文章\"\n                      >\n                        <Eye className=\"w-4 h-4 group-hover:scale-110 transition-transform\" />\n                      </Link>\n                      <Link\n                        href={`/admin/edit-post/${post.id}`}\n                        className=\"text-green-600 dark:text-green-400 hover:text-green-700 dark:hover:text-green-300 transition-all duration-200 p-2 rounded-lg hover:bg-green-50 dark:hover:bg-green-900/20 group\"\n                        title=\"編輯文章\"\n                      >\n                        <Edit className=\"w-4 h-4 group-hover:scale-110 transition-transform\" />\n                      </Link>\n                      <button\n                        onClick={() => handleDelete(post.id, post.title)}\n                        disabled={deletingId === post.id}\n                        className=\"text-red-600 dark:text-red-400 hover:text-red-700 dark:hover:text-red-300 transition-all duration-200 p-2 rounded-lg hover:bg-red-50 dark:hover:bg-red-900/20 disabled:opacity-50 disabled:cursor-not-allowed group\"\n                        title=\"刪除文章\"\n                      >\n                        {deletingId === post.id ? (\n                          <div className=\"w-4 h-4 animate-spin rounded-full border-2 border-current border-t-transparent\" />\n                        ) : (\n                          <Trash2 className=\"w-4 h-4 group-hover:scale-110 transition-transform\" />\n                        )}\n                      </button>\n                    </div>\n                  </td>\n                </tr>\n              ))}\n            </tbody>\n          </table>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAEA;AACA;AAAA;AAAA;AAAA;;;AAPA;;;;;;AAae,SAAS,kBAAkB,EAAE,OAAO,YAAY,EAA0B;;IACvF,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAC5D,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,eAAe,OAAO,QAAgB;QAC1C,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE,MAAM,WAAW,CAAC,GAAG;YAC3C;QACF;QAEA,cAAc;QAEd,IAAI;YACF,MAAM,WAAW,CAAA,GAAA,mIAAA,CAAA,eAAY,AAAD;YAC5B,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SACrB,IAAI,CAAC,SACL,MAAM,GACN,EAAE,CAAC,MAAM;YAEZ,IAAI,OAAO;gBACT,MAAM;YACR;YAEA,iBAAiB;YACjB,SAAS,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;YAE1C,gBAAgB;YAChB,OAAO,OAAO;QAChB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wBAAwB;YACtC,MAAM;QACR,SAAU;YACR,cAAc;QAChB;IACF;IAEA,IAAI,MAAM,MAAM,KAAK,GAAG;QACtB,qBACE,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAG,WAAU;8BAAqC;;;;;;8BACnD,6LAAC,+JAAA,CAAA,UAAI;oBACH,MAAK;oBACL,WAAU;8BACX;;;;;;;;;;;;IAKP;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;wCAAuB,MAAK;wCAAO,QAAO;wCAAe,SAAQ;kDAC9E,cAAA,6LAAC;4CAAK,eAAc;4CAAQ,gBAAe;4CAAQ,aAAa;4CAAG,GAAE;;;;;;;;;;;;;;;;8CAGzE,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAE,WAAU;sDAA4C;;;;;;sDACzD,6LAAC;4CAAE,WAAU;sDAAsC,MAAM,MAAM;;;;;;;;;;;;;;;;;;;;;;;kCAKrE,6LAAC;wBAAI,WAAU;wBAAyE,OAAO;4BAAE,gBAAgB;wBAAO;kCACtH,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;wCAA6C,MAAK;wCAAO,QAAO;wCAAe,SAAQ;kDACpG,cAAA,6LAAC;4CAAK,eAAc;4CAAQ,gBAAe;4CAAQ,aAAa;4CAAG,GAAE;;;;;;;;;;;;;;;;8CAGzE,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAE,WAAU;sDAA4C;;;;;;sDACzD,6LAAC;4CAAE,WAAU;sDACV,MAAM,MAAM,CAAC,CAAA;gDACZ,MAAM,QAAQ,IAAI;gDAClB,MAAM,WAAW,IAAI,KAAK,KAAK,UAAU;gDACzC,OAAO,SAAS,YAAY,OAAO,MAAM,YAAY;4CACvD,GAAG,MAAM;;;;;;;;;;;;;;;;;;;;;;;kCAMjB,6LAAC;wBAAI,WAAU;wBAAyE,OAAO;4BAAE,gBAAgB;wBAAO;kCACtH,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;wCAA2C,MAAK;wCAAO,QAAO;wCAAe,SAAQ;kDAClG,cAAA,6LAAC;4CAAK,eAAc;4CAAQ,gBAAe;4CAAQ,aAAa;4CAAG,GAAE;;;;;;;;;;;;;;;;8CAGzE,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAE,WAAU;sDAA4C;;;;;;sDACzD,6LAAC;4CAAE,WAAU;sDACV,MAAM,MAAM,CAAC,CAAA;gDACZ,MAAM,UAAU,IAAI;gDACpB,QAAQ,OAAO,CAAC,QAAQ,OAAO,KAAK;gDACpC,OAAO,IAAI,KAAK,KAAK,UAAU,IAAI;4CACrC,GAAG,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQnB,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAM,WAAU;;0CACf,6LAAC;gCAAM,WAAU;0CACf,cAAA,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;sDAAyF;;;;;;sDAGvG,6LAAC;4CAAG,WAAU;sDAAyF;;;;;;sDAGvG,6LAAC;4CAAG,WAAU;sDAAyF;;;;;;sDAGvG,6LAAC;4CAAG,WAAU;sDAA0F;;;;;;;;;;;;;;;;;0CAK5G,6LAAC;gCAAM,WAAU;0CACd,MAAM,GAAG,CAAC,CAAC,MAAM,sBAChB,6LAAC;wCAAiB,WAAU;wCAAiE,OAAO;4CAAE,gBAAgB,GAAG,QAAQ,KAAK,CAAC,CAAC;wCAAC;;0DACvI,6LAAC;gDAAG,WAAU;0DACZ,cAAA,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;;0EACC,6LAAC;gEAAI,WAAU;0EACZ,KAAK,KAAK;;;;;;0EAEb,6LAAC;gEAAI,WAAU;;oEACZ,KAAK,OAAO,CAAC,SAAS,CAAC,GAAG;oEAC1B,KAAK,OAAO,CAAC,MAAM,GAAG,OAAO;;;;;;;;;;;;;;;;;;;;;;;0DAKtC,6LAAC;gDAAG,WAAU;0DACZ,cAAA,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,6MAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;wDACnB,IAAI,KAAK,KAAK,UAAU,EAAE,kBAAkB,CAAC;;;;;;;;;;;;0DAGlD,6LAAC;gDAAG,WAAU;0DACZ,cAAA,6LAAC;oDAAI,WAAU;8DACZ,IAAI,KAAK,KAAK,UAAU,EAAE,kBAAkB,CAAC;;;;;;;;;;;0DAGlD,6LAAC;gDAAG,WAAU;0DACZ,cAAA,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,+JAAA,CAAA,UAAI;4DACH,MAAM,CAAC,OAAO,EAAE,KAAK,EAAE,EAAE;4DACzB,WAAU;4DACV,OAAM;sEAEN,cAAA,6LAAC,mMAAA,CAAA,MAAG;gEAAC,WAAU;;;;;;;;;;;sEAEjB,6LAAC,+JAAA,CAAA,UAAI;4DACH,MAAM,CAAC,iBAAiB,EAAE,KAAK,EAAE,EAAE;4DACnC,WAAU;4DACV,OAAM;sEAEN,cAAA,6LAAC,8MAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;;;;;;sEAElB,6LAAC;4DACC,SAAS,IAAM,aAAa,KAAK,EAAE,EAAE,KAAK,KAAK;4DAC/C,UAAU,eAAe,KAAK,EAAE;4DAChC,WAAU;4DACV,OAAM;sEAEL,eAAe,KAAK,EAAE,iBACrB,6LAAC;gEAAI,WAAU;;;;;qFAEf,6LAAC,6MAAA,CAAA,SAAM;gEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;uCAlDnB,KAAK,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+DhC;GAlMwB;;QAGP,qIAAA,CAAA,YAAS;;;KAHF", "debugId": null}}]}