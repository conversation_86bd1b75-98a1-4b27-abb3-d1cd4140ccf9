[{"C:\\Users\\<USER>\\Desktop\\test2\\my-supabase-blog\\src\\app\\auth\\signout\\route.ts": "1", "C:\\Users\\<USER>\\Desktop\\test2\\my-supabase-blog\\src\\app\\layout.tsx": "2", "C:\\Users\\<USER>\\Desktop\\test2\\my-supabase-blog\\src\\app\\login\\page.tsx": "3", "C:\\Users\\<USER>\\Desktop\\test2\\my-supabase-blog\\src\\app\\page.tsx": "4", "C:\\Users\\<USER>\\Desktop\\test2\\my-supabase-blog\\src\\lib\\auth.ts": "5", "C:\\Users\\<USER>\\Desktop\\test2\\my-supabase-blog\\src\\lib\\supabase\\client.ts": "6", "C:\\Users\\<USER>\\Desktop\\test2\\my-supabase-blog\\src\\lib\\supabase\\middleware.ts": "7", "C:\\Users\\<USER>\\Desktop\\test2\\my-supabase-blog\\src\\lib\\supabase\\server.ts": "8", "C:\\Users\\<USER>\\Desktop\\test2\\my-supabase-blog\\src\\types\\database.ts": "9", "C:\\Users\\<USER>\\Desktop\\test2\\my-supabase-blog\\src\\app\\admin\\edit-post\\[id]\\edit-post-form.tsx": "10", "C:\\Users\\<USER>\\Desktop\\test2\\my-supabase-blog\\src\\app\\admin\\edit-post\\[id]\\page.tsx": "11", "C:\\Users\\<USER>\\Desktop\\test2\\my-supabase-blog\\src\\app\\admin\\manage-posts\\manage-posts-client.tsx": "12", "C:\\Users\\<USER>\\Desktop\\test2\\my-supabase-blog\\src\\app\\admin\\manage-posts\\page.tsx": "13", "C:\\Users\\<USER>\\Desktop\\test2\\my-supabase-blog\\src\\app\\admin\\new-post\\new-post-form.tsx": "14", "C:\\Users\\<USER>\\Desktop\\test2\\my-supabase-blog\\src\\app\\admin\\new-post\\page.tsx": "15", "C:\\Users\\<USER>\\Desktop\\test2\\my-supabase-blog\\src\\app\\posts\\[id]\\page.tsx": "16", "C:\\Users\\<USER>\\Desktop\\test2\\my-supabase-blog\\src\\components\\cherry-editor.tsx": "17", "C:\\Users\\<USER>\\Desktop\\test2\\my-supabase-blog\\src\\components\\markdown-renderer.tsx": "18", "C:\\Users\\<USER>\\Desktop\\test2\\my-supabase-blog\\src\\components\\theme-provider.tsx": "19", "C:\\Users\\<USER>\\Desktop\\test2\\my-supabase-blog\\src\\components\\theme-toggle.tsx": "20"}, {"size": 308, "mtime": 1751166056094, "results": "21", "hashOfConfig": "22"}, {"size": 1024, "mtime": 1751167352407, "results": "23", "hashOfConfig": "22"}, {"size": 5661, "mtime": 1751167987405, "results": "24", "hashOfConfig": "22"}, {"size": 4697, "mtime": 1751167494578, "results": "25", "hashOfConfig": "22"}, {"size": 1008, "mtime": 1751166035214, "results": "26", "hashOfConfig": "22"}, {"size": 212, "mtime": 1751165729262, "results": "27", "hashOfConfig": "22"}, {"size": 1896, "mtime": 1751165746722, "results": "28", "hashOfConfig": "22"}, {"size": 790, "mtime": 1751166018222, "results": "29", "hashOfConfig": "22"}, {"size": 236, "mtime": 1751168129038, "results": "30", "hashOfConfig": "22"}, {"size": 3606, "mtime": 1751167699165, "results": "31", "hashOfConfig": "22"}, {"size": 1323, "mtime": 1751168063628, "results": "32", "hashOfConfig": "22"}, {"size": 6946, "mtime": 1751167671694, "results": "33", "hashOfConfig": "22"}, {"size": 2436, "mtime": 1751167641609, "results": "34", "hashOfConfig": "22"}, {"size": 3194, "mtime": 1751167573114, "results": "35", "hashOfConfig": "22"}, {"size": 773, "mtime": 1751167558328, "results": "36", "hashOfConfig": "22"}, {"size": 5748, "mtime": 1751168084206, "results": "37", "hashOfConfig": "22"}, {"size": 2560, "mtime": 1751168233047, "results": "38", "hashOfConfig": "22"}, {"size": 2584, "mtime": 1751168247582, "results": "39", "hashOfConfig": "22"}, {"size": 345, "mtime": 1751168365895, "results": "40", "hashOfConfig": "22"}, {"size": 1539, "mtime": 1751167337117, "results": "41", "hashOfConfig": "22"}, {"filePath": "42", "messages": "43", "suppressedMessages": "44", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "mrqay8", {"filePath": "45", "messages": "46", "suppressedMessages": "47", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "48", "messages": "49", "suppressedMessages": "50", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "51", "messages": "52", "suppressedMessages": "53", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "54", "messages": "55", "suppressedMessages": "56", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "57", "messages": "58", "suppressedMessages": "59", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "60", "messages": "61", "suppressedMessages": "62", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "63", "messages": "64", "suppressedMessages": "65", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "66", "messages": "67", "suppressedMessages": "68", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "69", "messages": "70", "suppressedMessages": "71", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "72", "messages": "73", "suppressedMessages": "74", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "75", "messages": "76", "suppressedMessages": "77", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "78", "messages": "79", "suppressedMessages": "80", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "81", "messages": "82", "suppressedMessages": "83", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "84", "messages": "85", "suppressedMessages": "86", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "87", "messages": "88", "suppressedMessages": "89", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "90", "messages": "91", "suppressedMessages": "92", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "93", "messages": "94", "suppressedMessages": "95", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "96", "messages": "97", "suppressedMessages": "98", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "99", "messages": "100", "suppressedMessages": "101", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Desktop\\test2\\my-supabase-blog\\src\\app\\auth\\signout\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\test2\\my-supabase-blog\\src\\app\\layout.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\test2\\my-supabase-blog\\src\\app\\login\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\test2\\my-supabase-blog\\src\\app\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\test2\\my-supabase-blog\\src\\lib\\auth.ts", [], [], "C:\\Users\\<USER>\\Desktop\\test2\\my-supabase-blog\\src\\lib\\supabase\\client.ts", [], [], "C:\\Users\\<USER>\\Desktop\\test2\\my-supabase-blog\\src\\lib\\supabase\\middleware.ts", [], [], "C:\\Users\\<USER>\\Desktop\\test2\\my-supabase-blog\\src\\lib\\supabase\\server.ts", [], [], "C:\\Users\\<USER>\\Desktop\\test2\\my-supabase-blog\\src\\types\\database.ts", [], [], "C:\\Users\\<USER>\\Desktop\\test2\\my-supabase-blog\\src\\app\\admin\\edit-post\\[id]\\edit-post-form.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\test2\\my-supabase-blog\\src\\app\\admin\\edit-post\\[id]\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\test2\\my-supabase-blog\\src\\app\\admin\\manage-posts\\manage-posts-client.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\test2\\my-supabase-blog\\src\\app\\admin\\manage-posts\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\test2\\my-supabase-blog\\src\\app\\admin\\new-post\\new-post-form.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\test2\\my-supabase-blog\\src\\app\\admin\\new-post\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\test2\\my-supabase-blog\\src\\app\\posts\\[id]\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\test2\\my-supabase-blog\\src\\components\\cherry-editor.tsx", ["102", "103", "104", "105"], [], "C:\\Users\\<USER>\\Desktop\\test2\\my-supabase-blog\\src\\components\\markdown-renderer.tsx", ["106", "107", "108"], [], "C:\\Users\\<USER>\\Desktop\\test2\\my-supabase-blog\\src\\components\\theme-provider.tsx", ["109"], [], "C:\\Users\\<USER>\\Desktop\\test2\\my-supabase-blog\\src\\components\\theme-toggle.tsx", [], [], {"ruleId": "110", "severity": 2, "message": "111", "line": 17, "column": 3, "nodeType": null, "messageId": "112", "endLine": 17, "endColumn": 14}, {"ruleId": "113", "severity": 2, "message": "114", "line": 20, "column": 28, "nodeType": "115", "messageId": "116", "endLine": 20, "endColumn": 31, "suggestions": "117"}, {"ruleId": "113", "severity": 2, "message": "114", "line": 49, "column": 16, "nodeType": "115", "messageId": "116", "endLine": 49, "endColumn": 19, "suggestions": "118"}, {"ruleId": "119", "severity": 1, "message": "120", "line": 66, "column": 6, "nodeType": "121", "endLine": 66, "endColumn": 8, "suggestions": "122"}, {"ruleId": "113", "severity": 2, "message": "114", "line": 13, "column": 28, "nodeType": "115", "messageId": "116", "endLine": 13, "endColumn": 31, "suggestions": "123"}, {"ruleId": "113", "severity": 2, "message": "114", "line": 37, "column": 16, "nodeType": "115", "messageId": "116", "endLine": 37, "endColumn": 19, "suggestions": "124"}, {"ruleId": "119", "severity": 1, "message": "125", "line": 59, "column": 6, "nodeType": "121", "endLine": 59, "endColumn": 8, "suggestions": "126"}, {"ruleId": "113", "severity": 2, "message": "114", "line": 8, "column": 18, "nodeType": "115", "messageId": "116", "endLine": 8, "endColumn": 21, "suggestions": "127"}, "@typescript-eslint/no-unused-vars", "'placeholder' is assigned a value but never used.", "unusedVar", "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["128", "129"], ["130", "131"], "react-hooks/exhaustive-deps", "React Hook useEffect has missing dependencies: 'height', 'onChange', and 'value'. Either include them or remove the dependency array. If 'onChange' changes too often, find the parent component that defines it and wrap that definition in useCallback.", "ArrayExpression", ["132"], ["133", "134"], ["135", "136"], "React Hook useEffect has a missing dependency: 'content'. Either include it or remove the dependency array.", ["137"], ["138", "139"], {"messageId": "140", "fix": "141", "desc": "142"}, {"messageId": "143", "fix": "144", "desc": "145"}, {"messageId": "140", "fix": "146", "desc": "142"}, {"messageId": "143", "fix": "147", "desc": "145"}, {"desc": "148", "fix": "149"}, {"messageId": "140", "fix": "150", "desc": "142"}, {"messageId": "143", "fix": "151", "desc": "145"}, {"messageId": "140", "fix": "152", "desc": "142"}, {"messageId": "143", "fix": "153", "desc": "145"}, {"desc": "154", "fix": "155"}, {"messageId": "140", "fix": "156", "desc": "142"}, {"messageId": "143", "fix": "157", "desc": "145"}, "suggestUnknown", {"range": "158", "text": "159"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "160", "text": "161"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", {"range": "162", "text": "159"}, {"range": "163", "text": "161"}, "Update the dependencies array to be: [height, onChange, value]", {"range": "164", "text": "165"}, {"range": "166", "text": "159"}, {"range": "167", "text": "161"}, {"range": "168", "text": "159"}, {"range": "169", "text": "161"}, "Update the dependencies array to be: [content]", {"range": "170", "text": "171"}, {"range": "172", "text": "159"}, {"range": "173", "text": "161"}, [444, 447], "unknown", [444, 447], "never", [1298, 1301], [1298, 1301], [1611, 1613], "[height, onChange, value]", [348, 351], [348, 351], [1074, 1077], [1074, 1077], [1588, 1590], "[content]", [188, 191], [188, 191]]