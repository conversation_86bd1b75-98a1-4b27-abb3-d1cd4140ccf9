{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_f58e8655._.js", "server/edge/chunks/[root-of-the-server]__c6c91fdf._.js", "server/edge/chunks/edge-wrapper_c6630286.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*){(\\\\.json)}?", "originalSource": "/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "wC6mVzmAcSTFXEAQa4K0/xSyQABPm2lxWxxuURttMKc=", "__NEXT_PREVIEW_MODE_ID": "93e114644d3df8a6b6c58f69ea23ed89", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "84a6e71b304a34b2177bec2210e111c3155c2110fea977a6a4dacc657d0c69ea", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "6096970b74d6a1d7248f16cea9a39be91b9ceecc96df5d356258d82ac6d57744"}}}, "instrumentation": null, "functions": {}}