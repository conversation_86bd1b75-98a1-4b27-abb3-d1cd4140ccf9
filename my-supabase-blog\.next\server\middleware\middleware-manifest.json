{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_f58e8655._.js", "server/edge/chunks/[root-of-the-server]__c6c91fdf._.js", "server/edge/chunks/edge-wrapper_c6630286.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*){(\\\\.json)}?", "originalSource": "/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "wC6mVzmAcSTFXEAQa4K0/xSyQABPm2lxWxxuURttMKc=", "__NEXT_PREVIEW_MODE_ID": "5dcb65c5536fee8eee2b3c0f208c590b", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "889d288188bb77f4c67f90b7f87d4a6cbbd102bc8c49ddcc3bee08d15853b5f9", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "1fa2a880a388ed13ec3c6ac2d5742e51295baf6a4501c8569cca698b26b0de34"}}}, "instrumentation": null, "functions": {}}