{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_f58e8655._.js", "server/edge/chunks/[root-of-the-server]__c6c91fdf._.js", "server/edge/chunks/edge-wrapper_c6630286.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*){(\\\\.json)}?", "originalSource": "/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "wC6mVzmAcSTFXEAQa4K0/xSyQABPm2lxWxxuURttMKc=", "__NEXT_PREVIEW_MODE_ID": "4348e13b8de6992a6ff9d3943a4904fa", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "31c402b50f28ca776b1b0fd3265d5591256e0dc04df44d76558f63f64dcf9da7", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "dc878bef37586d4acd478785c7c7bca929857411d71b28aa80b74edbdc623028"}}}, "instrumentation": null, "functions": {}}